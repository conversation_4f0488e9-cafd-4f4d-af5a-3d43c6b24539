package com.example.addon.modules;

import com.example.addon.AddonTemplate;
import meteordevelopment.meteorclient.events.render.Render3DEvent;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.renderer.ShapeMode;
import meteordevelopment.meteorclient.settings.ColorSetting;
import meteordevelopment.meteorclient.utils.render.color.Color;
import meteordevelopment.meteorclient.utils.render.color.SettingColor;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Box;
import meteordevelopment.meteorclient.settings.DoubleSetting;
import meteordevelopment.meteorclient.settings.Setting;
import meteordevelopment.meteorclient.settings.SettingGroup;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.systems.modules.Modules;
import meteordevelopment.meteorclient.utils.entity.TargetUtils;
import meteordevelopment.meteorclient.utils.entity.SortPriority;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.client.MinecraftClient;
import net.minecraft.entity.Entity;
import net.minecraft.entity.LivingEntity;
import net.minecraft.util.math.Vec3d;
import java.util.function.Predicate;

public class ModuleExample extends Module {
   private final SettingGroup sgGeneral = this.settings.getDefaultGroup();
   private final SettingGroup sgRender = this.settings.createGroup("Render");

   private final Setting<Double> scale = sgGeneral.add(new DoubleSetting.Builder()
       .name("scale")
       .description("The size of the marker.")
       .defaultValue(2.0d)
       .range(0.5d, 10.0d)
       .build()
   );

   private final Setting<Double> aimSpeed = sgGeneral.add(new DoubleSetting.Builder()
       .name("aim-speed")
       .description("The speed at which the player aims at the target.")
       .defaultValue(0.5d)
       .range(0.1d, 1.0d)
       .build()
   );

   private final Setting<SettingColor> color = sgRender.add(new ColorSetting.Builder()
       .name("color")
       .description("The color of the marker.")
       .defaultValue(Color.MAGENTA)
       .build()
   );

   private LivingEntity target = null;

   public ModuleExample() {
       super(AddonTemplate.CATEGORY, "world-origin", "An example module that highlights the center of the world.");
   }

   @EventHandler
   private void onTick(TickEvent.Pre event) {
       if (mc.player == null) return;

       LivingEntity oldTarget = this.target;

       Targets targetsModule = Modules.get().get(Targets.class);
       if (targetsModule == null || !targetsModule.isActive()) {
           this.target = null;
           if (oldTarget != null) info("Lost target (Targets module not active).");
           return;
       }

       Predicate<Entity> targetPredicate = entity -> {
           if (entity.equals(mc.player)) return false;
           
           // Use the targetsModule variable from the outer scope instead of redeclaring it
           if (targetsModule == null || !targetsModule.isActive()) {
               return false;
           }
           
           // Use the new shouldTarget method to check if we should target this entity
           return targetsModule.shouldTarget(entity);
       };

       Entity foundTarget = TargetUtils.get(targetPredicate, SortPriority.LowestDistance);

       if (foundTarget instanceof LivingEntity) {
           this.target = (LivingEntity) foundTarget;
       } else {
           this.target = null;
       }

       if (this.target != null && this.target != oldTarget) {
           info("New target acquired: %s (%.2f blocks away)", this.target.getName().getString(), mc.player.distanceTo(this.target));
       } else if (this.target == null && oldTarget != null) {
           info("Lost target.");
       }
   }

   @EventHandler
   private void onRender3d(Render3DEvent event) {
       if (mc.player == null) return;

       if (target != null) {
           Vec3d playerPos = mc.player.getEyePos();
           Box hitbox = target.getBoundingBox();
           Vec3d targetPos = hitbox.getCenter();

           double x = targetPos.x - playerPos.x;
           double y = targetPos.y - playerPos.y;
           double z = targetPos.z - playerPos.z;

           double distanceXZ = Math.sqrt(x * x + z * z);

           float targetYaw = (float) Math.toDegrees(Math.atan2(z, x)) - 90.0F;
           float targetPitch = (float) Math.toDegrees(-Math.atan2(y, distanceXZ));

           float currentYaw = mc.player.getYaw();
           float deltaYaw = targetYaw - currentYaw;
           if (deltaYaw > 180) deltaYaw -= 360;
           if (deltaYaw < -180) deltaYaw += 360;
           
           float frameAimSpeed = (float) (aimSpeed.get() * 25.0 * event.frameTime * 60);
           mc.player.setYaw(currentYaw + deltaYaw * frameAimSpeed);

           float currentPitch = mc.player.getPitch();
           float deltaPitch = targetPitch - currentPitch;
           mc.player.setPitch(currentPitch + deltaPitch * frameAimSpeed);
       }

       Box marker = new Box(BlockPos.ORIGIN);
       marker = marker.stretch(
           scale.get() * marker.getLengthX(),
           scale.get() * marker.getLengthY(),
           scale.get() * marker.getLengthZ()
       );

       event.renderer.box(marker, color.get(), color.get(), ShapeMode.Both, 0);
   }
}