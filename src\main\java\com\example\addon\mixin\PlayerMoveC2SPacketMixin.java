package com.example.addon.mixin;

import net.minecraft.network.packet.c2s.play.PlayerMoveC2SPacket;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

@Mixin(PlayerMoveC2SPacket.class)
public class PlayerMoveC2SPacketMixin {

    @Inject(method = "apply", at = @At("HEAD"))
    private void onPlayerMoveC2SPacketApply(net.minecraft.network.listener.ServerPlayPacketListener listener, CallbackInfo ci) {
        // Placeholder for future logic
    }
}