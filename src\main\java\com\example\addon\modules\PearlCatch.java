package com.example.addon.modules;

import com.example.addon.AddonTemplate;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.utils.player.FindItemResult;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import net.minecraft.item.Items;
import net.minecraft.util.Hand;
import meteordevelopment.meteorclient.settings.DoubleSetting;
import meteordevelopment.meteorclient.settings.BoolSetting;
import meteordevelopment.meteorclient.settings.Setting;
import meteordevelopment.meteorclient.settings.SettingGroup;
import meteordevelopment.orbit.EventHandler;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.events.packets.PacketEvent;
import net.minecraft.network.packet.c2s.play.PlayerInteractItemC2SPacket;

public class PearlCatch extends Module {
    private final SettingGroup sgGeneral = settings.getDefaultGroup();

    private final Setting<Boolean> throwPearl = sgGeneral.add(new BoolSetting.Builder()
        .name("throw-pearl")
        .description("If true, throws pearl then wind charge when activated. If false, throws wind charge when you throw a pearl.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Double> pearlDelay = sgGeneral.add(new DoubleSetting.Builder()
        .name("pearl-delay")
        .description("Delay in ticks between throwing pearl and wind charge.")
        .defaultValue(3.0)
        .min(0.0)
        .max(20.0)
        .sliderMin(0.0)
        .sliderMax(20.0)
        .build()
    );

    private final Setting<Double> swapDelay = sgGeneral.add(new DoubleSetting.Builder()
        .name("swap-delay")
        .description("Delay in ticks after swapping to target item.")
        .defaultValue(1.0)
        .min(0.0)
        .max(10.0)
        .sliderMin(0.0)
        .sliderMax(10.0)
        .build()
    );

    private final Setting<Double> swapBackDelay = sgGeneral.add(new DoubleSetting.Builder()
        .name("swap-back-delay")
        .description("Delay in ticks before swapping back to original slot.")
        .defaultValue(2.0)
        .min(0.0)
        .max(10.0)
        .sliderMin(0.0)
        .sliderMax(10.0)
        .build()
    );

    public PearlCatch() {
        super(AddonTemplate.CATEGORY, "pearl-catch", "Throws wind charges to catch ender pearls.");
    }

    private int timer = -1;
    private int state = 0;
    private int originalHotbarSlot = -1;
    private FindItemResult pearlItem;
    private FindItemResult windChargeItem;
    private boolean pearlThrown = false;

    @Override
    public void onActivate() {
        if (throwPearl.get()) {
            // Auto mode: throw pearl then wind charge
            startAutoSequence();
        } else {
            // Manual mode: wait for pearl throw
            info("Waiting for you to throw an ender pearl...");
        }
    }

    @Override
    public void onDeactivate() {
        resetState();
    }

    private void startAutoSequence() {
        // Find ender pearl and wind charge in hotbar
        pearlItem = InvUtils.findInHotbar(Items.ENDER_PEARL);
        windChargeItem = InvUtils.findInHotbar(Items.WIND_CHARGE);

        if (!pearlItem.found()) {
            error("No ender pearl found in hotbar.");
            toggle();
            return;
        }

        if (!windChargeItem.found()) {
            error("No wind charge found in hotbar.");
            toggle();
            return;
        }

        // Store original slot - use getSelectedSlot() instead of selectedSlot
        FindItemResult originalSlotResult = InvUtils.findInHotbar(mc.player.getMainHandStack().getItem());
        originalHotbarSlot = originalSlotResult.found() ? originalSlotResult.slot() : -1;

        // Start sequence
        state = 1;
        timer = swapDelay.get().intValue();
    }

    @EventHandler
    private void onTick(TickEvent.Pre event) {
        if (timer > 0) {
            timer--;
            return;
        }

        if (throwPearl.get()) {
            // Auto mode sequence
            switch (state) {
                case 1:
                    // Swap to ender pearl
                    InvUtils.swap(pearlItem.slot(), false);
                    state = 2;
                    timer = swapDelay.get().intValue();
                    break;
                case 2:
                    // Throw ender pearl
                    mc.interactionManager.interactItem(mc.player, Hand.MAIN_HAND);
                    state = 3;
                    timer = pearlDelay.get().intValue();
                    break;
                case 3:
                    // Swap to wind charge
                    InvUtils.swap(windChargeItem.slot(), false);
                    state = 4;
                    timer = swapDelay.get().intValue();
                    break;
                case 4:
                    // Throw wind charge
                    mc.interactionManager.interactItem(mc.player, Hand.MAIN_HAND);
                    state = 5;
                    timer = swapBackDelay.get().intValue();
                    break;
                case 5:
                    // Swap back to original slot
                    if (originalHotbarSlot != -1) {
                        InvUtils.swap(originalHotbarSlot, false);
                    }
                    info("Pearl catch sequence completed.");
                    toggle();
                    break;
            }
        } else if (pearlThrown) {
            // Manual mode: pearl was thrown, now throw wind charge
            switch (state) {
                case 1:
                    // Swap to wind charge
                    InvUtils.swap(windChargeItem.slot(), false);
                    state = 2;
                    timer = swapDelay.get().intValue();
                    break;
                case 2:
                    // Throw wind charge
                    mc.interactionManager.interactItem(mc.player, Hand.MAIN_HAND);
                    state = 3;
                    timer = swapBackDelay.get().intValue();
                    break;
                case 3:
                    // Swap back to original slot
                    if (originalHotbarSlot != -1) {
                        InvUtils.swap(originalHotbarSlot, false);
                    }
                    info("Wind charge thrown to catch pearl.");
                    toggle();
                    break;
            }
        }
    }

    @EventHandler
    private void onPacketSend(PacketEvent.Send event) {
        if (!throwPearl.get() && event.packet instanceof PlayerInteractItemC2SPacket) {
            // Check if player is throwing an ender pearl
            if (mc.player.getMainHandStack().getItem() == Items.ENDER_PEARL) {
                onPearlThrown();
            }
        }
    }

    private void onPearlThrown() {
        if (pearlThrown) return; // Already processing

        // Find wind charge in hotbar
        windChargeItem = InvUtils.findInHotbar(Items.WIND_CHARGE);

        if (!windChargeItem.found()) {
            error("No wind charge found in hotbar to catch the pearl.");
            toggle();
            return;
        }

        // Store original slot - use getSelectedSlot() instead of selectedSlot
        FindItemResult originalSlotResult = InvUtils.findInHotbar(mc.player.getMainHandStack().getItem());
        originalHotbarSlot = originalSlotResult.found() ? originalSlotResult.slot() : -1;

        pearlThrown = true;
        state = 1;
        timer = pearlDelay.get().intValue();
        info("Ender pearl detected, preparing wind charge...");
    }

    private void resetState() {
        timer = -1;
        state = 0;
        originalHotbarSlot = -1;
        pearlThrown = false;
        pearlItem = null;
        windChargeItem = null;
    }
}