package com.example.addon.test;

import com.example.addon.utils.ModuleCommunication;
import net.minecraft.entity.Entity;
import net.minecraft.entity.player.PlayerEntity;

/**
 * Simple test class to verify sprint reset communication functionality.
 * This can be used to manually test the communication between AutoClicker and W-Tap.
 */
public class SprintResetTest {
    
    /**
     * Test the basic sprint reset request and response cycle.
     */
    public static void testSprintResetCommunication() {
        
        
        // Test 1: Initial state should be no request
        
        boolean initialRequest = ModuleCommunication.isSprintResetRequested();
        
        
        // Test 2: Request sprint reset
        
        // Note: We can't create a real entity in this test, so we'll use null
        ModuleCommunication.requestSprintReset(null);
        boolean afterRequest = ModuleCommunication.isSprintResetRequested();
        
        
        // Test 3: Clear request
        
        ModuleCommunication.clearSprintResetRequest();
        boolean afterClear = ModuleCommunication.isSprintResetRequested();
        
        
        // Test 4: Timeout test
        
        ModuleCommunication.requestSprintReset(null);
        
        
        // Simulate waiting longer than timeout
        try {
            Thread.sleep(150); // Wait longer than 100ms timeout
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        boolean afterTimeout = ModuleCommunication.isSprintResetRequested();
        
        
        
    }
    
    /**
     * Test the target-specific sprint reset functionality.
     */
    public static void testTargetSpecificSprintReset() {
        
        
        // Clear any existing requests
        ModuleCommunication.clearSprintResetRequest();
        
        // Test with null target (simulating entity)
        
        ModuleCommunication.requestSprintReset(null);
        Entity target = ModuleCommunication.getSprintResetTarget();
        
        
        boolean isForTarget = ModuleCommunication.isSprintResetForTarget(null);
        
        
        // Clean up
        ModuleCommunication.clearSprintResetRequest();
        
        
    }
    
    /**
     * Run all tests.
     */
    public static void runAllTests() {
        testSprintResetCommunication();
        
        testTargetSpecificSprintReset();
    }
}
