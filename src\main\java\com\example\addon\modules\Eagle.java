package com.example.addon.modules;

import com.example.addon.AddonTemplate;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.option.KeyBinding;
import net.minecraft.item.ItemStack;
import net.minecraft.util.hit.BlockHitResult;
import net.minecraft.util.hit.HitResult;

public class Eagle extends Module {
    private final SettingGroup sgGeneral = settings.getDefaultGroup();

    private final Setting<Boolean> enabled = sgGeneral.add(new BoolSetting.Builder()
        .name("enabled")
        .description("Whether the Eagle module is enabled.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Integer> delay = sgGeneral.add(new IntSetting.Builder()
        .name("delay")
        .description("Delay between actions in ticks (20 ticks = 1 second).")
        .defaultValue(5)
        .min(0)
        .sliderMax(20)
        .build()
    );

    private final Setting<Double> minAngle = sgGeneral.add(new DoubleSetting.Builder()
        .name("min-angle")
        .description("Minimum angle required for the action to trigger.")
        .defaultValue(45.0)
        .min(0.0)
        .max(90.0)
        .sliderMax(90.0)
        .build()
    );

    private final Setting<Boolean> requireHoldingBlock = sgGeneral.add(new BoolSetting.Builder()
        .name("require-holding-block")
        .description("Only trigger if holding a block.")
        .defaultValue(false)
        .build()
    );

    private final Setting<Boolean> requireSneaking = sgGeneral.add(new BoolSetting.Builder()
        .name("require-sneaking")
        .description("Only trigger if sneaking.")
        .defaultValue(false)
        .build()
    );

    private int ticksSinceLastAction = 0;

    public Eagle() {
        super(AddonTemplate.CATEGORY, "eagle", "Automatically sneaks at the end of a block.");
    }

    @EventHandler
    private void onTick(TickEvent.Post event) {
        if (!enabled.get()) return;

        if (ticksSinceLastAction < delay.get()) {
            ticksSinceLastAction++;
            return;
        }

        ticksSinceLastAction = 0;

        MinecraftClient mc = MinecraftClient.getInstance();
        if (mc.player == null || mc.world == null) return;

        // Check conditions
        boolean holdingBlock = !requireHoldingBlock.get() || isHoldingBlock(mc.player.getMainHandStack());
        boolean sneaking = !requireSneaking.get() || mc.player.isSneaking();
        boolean angleValid = isAngleValid(mc.player.getPitch(), minAngle.get());

        if (holdingBlock && sneaking && angleValid) {
            // Perform the action
            if (mc.player.isSneaking()) {
                KeyBinding.setKeyPressed(mc.options.sneakKey.getDefaultKey(), false);
            } else {
                KeyBinding.setKeyPressed(mc.options.sneakKey.getDefaultKey(), true);
            }
        }
    }

    private boolean isHoldingBlock(ItemStack stack) {
        return stack.getItem().isIn(net.minecraft.registry.tag.ItemTags.BLOCKS);
    }

    private boolean isAngleValid(float pitch, double minAngle) {
        return Math.abs(pitch) >= minAngle;
    }
}