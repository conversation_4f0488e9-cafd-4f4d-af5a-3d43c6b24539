package com.example.addon.utils;

import net.minecraft.util.math.MathHelper;
import net.minecraft.util.math.Vec3d;
import net.minecraft.entity.Entity;

import static meteordevelopment.meteorclient.MeteorClient.mc;

/**
 * Utility class for smooth aiming implementations.
 * Provides methods for calculating smooth rotations with configurable speed and interpolation.
 */
public class SmoothAimingUtils {
    
    public static void smoothAim(Vec3d targetPos, double smoothingFactor) {
        if (mc.player == null) return;

        float[] rotations = calculateExponentialSmoothRotations(
            mc.player.getYaw(),
            mc.player.getPitch(),
            targetPos,
            mc.player.getEyePos(),
            1.0, // Aim speed, can be a setting if needed
            1.0f, // Delta time, assuming 1.0 for simplicity or can be passed as argument
            smoothingFactor
        );

        mc.player.setYaw(rotations[0]);
        mc.player.setPitch(rotations[1]);
    }

    /**
     * Calculate smooth rotations to a target position with configurable speed.
     * 
     * @param currentYaw Current yaw angle of the player
     * @param currentPitch Current pitch angle of the player
     * @param targetPos Target position to aim at
     * @param playerPos Player's eye position
     * @param aimSpeed Speed factor for aiming (0.0 to 1.0)
     * @param deltaTime Time elapsed since last frame
     * @return Array containing [yaw, pitch] for smooth rotation
     */
    public static float[] calculateSmoothRotations(float currentYaw, float currentPitch, Vec3d targetPos, Vec3d playerPos, double aimSpeed, float deltaTime, double smoothingFactor) {
        // Calculate direction vector
        double dx = targetPos.x - playerPos.x;
        double dy = targetPos.y - playerPos.y;
        double dz = targetPos.z - playerPos.z;
        
        // Calculate target angles
        double distanceXZ = Math.sqrt(dx * dx + dz * dz);
        float targetYaw = (float) Math.toDegrees(Math.atan2(dz, dx)) - 90.0F;
        float targetPitch = (float) Math.toDegrees(-Math.atan2(dy, distanceXZ));
        
        // Clamp pitch to valid range
        targetPitch = MathHelper.clamp(targetPitch, -90.0F, 90.0F);

        // Calculate angle differences using shortest path (no normalization to avoid jumps)
        float deltaYaw = getShortestAngleDifference(currentYaw, targetYaw);
        float deltaPitch = targetPitch - currentPitch;
        
        // Apply smoothing based on aim speed and delta time
        // Clamp deltaTime to prevent issues with very high or low frame times
        float frameAimSpeed = (float) (aimSpeed * deltaTime * (1.0 - smoothingFactor));
        float smoothedYaw = currentYaw + deltaYaw * frameAimSpeed;
        float smoothedPitch = currentPitch + deltaPitch * frameAimSpeed;
        
        // Clamp pitch to valid range
        smoothedPitch = MathHelper.clamp(smoothedPitch, -90.0F, 90.0F);
        
        return new float[] {smoothedYaw, smoothedPitch};
    }
    
    /**
     * Calculate smooth rotations with exponential smoothing for even smoother aiming.
     * 
     * @param currentYaw Current yaw angle of the player
     * @param currentPitch Current pitch angle of the player
     * @param targetPos Target position to aim at
     * @param playerPos Player's eye position
     * @param aimSpeed Speed factor for aiming (0.0 to 1.0)
     * @param deltaTime Time elapsed since last frame
     * @param smoothingFactor Exponential smoothing factor (0.0 to 1.0, higher = smoother)
     * @return Array containing [yaw, pitch] for smooth rotation
     */
    public static float[] calculateExponentialSmoothRotations(float currentYaw, float currentPitch, Vec3d targetPos, Vec3d playerPos, double aimSpeed, float deltaTime, double smoothingFactor) {
        // Calculate direction vector
        double dx = targetPos.x - playerPos.x;
        double dy = targetPos.y - playerPos.y;
        double dz = targetPos.z - playerPos.z;
        
        // Calculate target angles
        double distanceXZ = Math.sqrt(dx * dx + dz * dz);
        float targetYaw = (float) Math.toDegrees(Math.atan2(dz, dx)) - 90.0F;
        float targetPitch = (float) Math.toDegrees(-Math.atan2(dy, distanceXZ));
        
        // Clamp pitch to valid range
        targetPitch = MathHelper.clamp(targetPitch, -90.0F, 90.0F);

        // Calculate angle differences using shortest path (no normalization to avoid jumps)
        float deltaYaw = getShortestAngleDifference(currentYaw, targetYaw);
        float deltaPitch = targetPitch - currentPitch;
        
        // Apply smoothing based on aim speed and delta time
        // Clamp deltaTime to prevent issues with very high or low frame times
        float frameAimSpeed = (float) (aimSpeed * deltaTime);
        
        // Apply exponential smoothing
        float expFactor = (float) smoothingFactor;
        // Exponential smoothing: new = old + factor * (target - old)
        float smoothedYaw = currentYaw + (deltaYaw * frameAimSpeed) * (1 - expFactor);
        float smoothedPitch = currentPitch + (deltaPitch * frameAimSpeed) * (1 - expFactor);
        
        // Clamp pitch to valid range
        smoothedPitch = MathHelper.clamp(smoothedPitch, -90.0F, 90.0F);
        
        return new float[] {smoothedYaw, smoothedPitch};
    }

    /**
     * Calculate the shortest angle difference between two angles.
     * This avoids sudden jumps when crossing the 360°/0° boundary.
     *
     * @param from Starting angle
     * @param to Target angle
     * @return Shortest angle difference (positive = clockwise, negative = counterclockwise)
     */
    public static float getShortestAngleDifference(float from, float to) {
        float diff = to - from;

        // Normalize difference to [-180, 180] range for shortest path
        while (diff > 180.0F) {
            diff -= 360.0F;
        }
        while (diff < -180.0F) {
            diff += 360.0F;
        }

        return diff;
    }
    
    /**
     * Calculate rotation speed needed to reach target angles within a specific time
     * 
     * @param currentYaw Current yaw angle
     * @param currentPitch Current pitch angle
     * @param targetYaw Target yaw angle
     * @param targetPitch Target pitch angle
     * @param rotationTime Time in seconds to reach target
     * @param deltaTime Time elapsed since last frame
     * @return Array containing [yawSpeed, pitchSpeed] for smooth rotation
     */
    public static float[] calculateRotationSpeed(float currentYaw, float currentPitch, float targetYaw, float targetPitch, double rotationTime, float deltaTime) {
        // Clamp pitch to valid range
        targetPitch = MathHelper.clamp(targetPitch, -90.0F, 90.0F);

        // Calculate angle differences using shortest path (no normalization to avoid jumps)
        float deltaYaw = getShortestAngleDifference(currentYaw, targetYaw);
        float deltaPitch = targetPitch - currentPitch;
        
        // Calculate speed needed to reach target in specified time
        float yawSpeed = (float) (deltaYaw / (rotationTime * 20)); // 20 TPS
        float pitchSpeed = (float) (deltaPitch / (rotationTime * 20)); // 20 TPS
        
        return new float[] {yawSpeed, pitchSpeed};
    }

    /**
     * Calculate smooth rotations to a randomized target position within an entity's bounds.
     * Combines smooth aiming with natural randomization for more human-like targeting.
     *
     * @param currentYaw Current yaw angle of the player
     * @param currentPitch Current pitch angle of the player
     * @param targetEntity Target entity to aim at
     * @param playerPos Player's eye position
     * @param aimSpeed Speed factor for aiming (0.0 to 1.0)
     * @param deltaTime Time elapsed since last frame
     * @param randomizationStrength How much randomization to apply (0.0 to 1.0)
     * @param proximityBias How much to prefer points closer to current crosshair (0.0 to 1.0)
     * @return Array containing [yaw, pitch] for smooth randomized rotation
     */
    public static float[] calculateRandomizedSmoothRotations(float currentYaw, float currentPitch, Entity targetEntity, Vec3d playerPos, double aimSpeed, float deltaTime, double smoothingFactor, double randomizationStrength, double proximityBias) {
        if (targetEntity == null || playerPos == null) {
            return new float[] {currentYaw, currentPitch};
        }

        // Get randomized target position
        Vec3d randomizedTargetPos = RandomizedAimingUtils.getRandomizedTargetPosition(
            targetEntity, playerPos, randomizationStrength, proximityBias
        );

        if (randomizedTargetPos == null) {
            return new float[] {currentYaw, currentPitch};
        }

        // Use existing smooth rotation calculation with randomized target
        return calculateSmoothRotations(currentYaw, currentPitch, randomizedTargetPos, playerPos, aimSpeed, deltaTime, smoothingFactor);
    }

    /**
     * Calculate smooth rotations with exponential smoothing and randomization.
     *
     * @param currentYaw Current yaw angle of the player
     * @param currentPitch Current pitch angle of the player
     * @param targetEntity Target entity to aim at
     * @param playerPos Player's eye position
     * @param aimSpeed Speed factor for aiming (0.0 to 1.0)
     * @param deltaTime Time elapsed since last frame
     * @param smoothingFactor Exponential smoothing factor (0.0 to 1.0)
     * @param randomizationStrength How much randomization to apply (0.0 to 1.0)
     * @param proximityBias How much to prefer points closer to current crosshair (0.0 to 1.0)
     * @return Array containing [yaw, pitch] for smooth randomized rotation
     */
    public static float[] calculateRandomizedExponentialSmoothRotations(float currentYaw, float currentPitch, Entity targetEntity, Vec3d playerPos, double aimSpeed, float deltaTime, double smoothingFactor, double randomizationStrength, double proximityBias) {
        if (targetEntity == null || playerPos == null) {
            return new float[] {currentYaw, currentPitch};
        }

        // Get randomized target position
        Vec3d randomizedTargetPos = RandomizedAimingUtils.getRandomizedTargetPosition(
            targetEntity, playerPos, randomizationStrength, proximityBias
        );

        if (randomizedTargetPos == null) {
            return new float[] {currentYaw, currentPitch};
        }

        // Use existing exponential smooth rotation calculation with randomized target
        return calculateExponentialSmoothRotations(currentYaw, currentPitch, randomizedTargetPos, playerPos, aimSpeed, deltaTime, smoothingFactor);
    }

    /**
     * Calculate smart randomized smooth rotations with body part targeting.
     *
     * @param currentYaw Current yaw angle of the player
     * @param currentPitch Current pitch angle of the player
     * @param targetEntity Target entity to aim at
     * @param playerPos Player's eye position
     * @param aimSpeed Speed factor for aiming (0.0 to 1.0)
     * @param deltaTime Time elapsed since last frame
     * @param randomizationStrength How much randomization to apply (0.0 to 1.0)
     * @param proximityBias How much to prefer points closer to current crosshair (0.0 to 1.0)
     * @param preferVitalAreas Whether to prefer head/torso areas
     * @return Array containing [yaw, pitch] for smart randomized rotation
     */
    public static float[] calculateSmartRandomizedSmoothRotations(
         float currentYaw, float currentPitch, Entity targetEntity, Vec3d playerPos,
         double aimSpeed, float deltaTime, double randomizationStrength, double proximityBias,
         boolean preferVitalAreas, double smoothingFactor) {
        if (targetEntity == null || playerPos == null) {
            return new float[] {currentYaw, currentPitch};
        }

        // Get smart randomized target position
        Vec3d randomizedTargetPos = RandomizedAimingUtils.getSmartRandomizedTargetPosition(
            targetEntity, playerPos, randomizationStrength, proximityBias, preferVitalAreas
        );

        if (randomizedTargetPos == null) {
            return new float[] {currentYaw, currentPitch};
        }

        // Use existing smooth rotation calculation with smart randomized target
        return calculateSmoothRotations(currentYaw, currentPitch, randomizedTargetPos, playerPos, aimSpeed, deltaTime, smoothingFactor);
    }

    /**
     * Add subtle randomization to existing rotation calculations.
     * This method can be used to add small random variations to any aiming system.
     *
     * @param rotations Array containing [yaw, pitch] rotations
     * @param randomizationAmount Maximum random offset in degrees
     * @return Array containing [randomized_yaw, randomized_pitch]
     */
    public static float[] addRandomizationToRotations(float[] rotations, double randomizationAmount) {
        if (rotations == null || rotations.length < 2 || randomizationAmount <= 0.0) {
            return rotations;
        }

        return RandomizedAimingUtils.addRandomizationToRotation(rotations[0], rotations[1], randomizationAmount);
    }
}