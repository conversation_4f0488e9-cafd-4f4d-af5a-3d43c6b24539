package com.example.addon.utils;

import net.minecraft.client.MinecraftClient;
import net.minecraft.client.option.KeyBinding;
import org.lwjgl.glfw.GLFW;

public class KeyPressSimulator {

    public static void pressKey(int keyCode) {
        setKeyState(keyCode, true);
    }

    public static void releaseKey(int keyCode) {
        setKeyState(keyCode, false);
    }

    private static void setKeyState(int keyCode, boolean pressed) {
        MinecraftClient mc = MinecraftClient.getInstance();
        if (mc != null && mc.getWindow() != null) {
            for (KeyBinding keyBinding : mc.options.allKeys) {
                if (keyBinding.matchesKey(keyCode, 0)) { // Assuming 0 for scancode
                    keyBinding.setPressed(pressed);
                    try {
                        // Introduce a small random delay to simulate human-like input
                        Thread.sleep(new java.util.Random().nextInt(5) + 1); // 1-5 ms delay
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
        }
    }

    // This method is a more robust way to simulate key presses by directly manipulating KeyBinding
    // However, it requires access to the KeyBinding object associated with the key code.
    // For a generic key code, we might need to iterate through all keybindings or use a different approach.
    public static void setKeyBindingState(KeyBinding keyBinding, boolean pressed) {
        keyBinding.setPressed(pressed);
    }
}