package com.example.addon.modules;

    import com.example.addon.AddonTemplate;
    import com.example.addon.utils.EnchantmentUtils;
    import meteordevelopment.meteorclient.events.entity.player.AttackEntityEvent;
    import meteordevelopment.meteorclient.systems.modules.Modules;
    import meteordevelopment.meteorclient.events.world.TickEvent;
    import meteordevelopment.meteorclient.settings.*;
    import meteordevelopment.meteorclient.systems.modules.Module;
    import meteordevelopment.meteorclient.utils.player.FindItemResult;
    import meteordevelopment.meteorclient.utils.player.InvUtils;
    import meteordevelopment.orbit.EventHandler;
    import net.minecraft.client.MinecraftClient;
    import net.minecraft.enchantment.Enchantments;
    import net.minecraft.item.ItemStack;
    import net.minecraft.item.Items;

    public class AttributeSwap extends Module {
        private final SettingGroup sgGeneral = settings.getDefaultGroup();
        private final MinecraftClient mc = MinecraftClient.getInstance();

        private final Setting<Integer> densityMaceSlot = sgGeneral.add(new IntSetting.Builder()
            .name("density-mace-slot")
            .description("The slot of the density enchanted mace.")
            .defaultValue(1)
            .min(1)
            .max(9)
            .sliderMin(1)
            .sliderMax(9)
            .build()
        );

        private final Setting<Integer> breachMaceSlot = sgGeneral.add(new IntSetting.Builder()
            .name("breach-mace-slot")
            .description("The slot of the breach enchanted mace.")
            .defaultValue(2)
            .min(1)
            .max(9)
            .sliderMin(1)
            .sliderMax(9)
            .build()
        );

        private final Setting<Double> minFallDistance = sgGeneral.add(new DoubleSetting.Builder()
            .name("min-fall-distance")
            .description("Minimum fall distance to trigger density mace swap.")
            .defaultValue(3.0)
            .min(0.0)
            .sliderMin(0.0)
            .build()
        );

        private final Setting<Boolean> autoDetectMaces = sgGeneral.add(new BoolSetting.Builder()
            .name("auto-detect-maces")
            .description("Automatically detects mace slots based on enchantments.")
            .defaultValue(true)
            .build()
        );

        private int originalSlot = -1;
        private int swapBackDelay = -1;

        public AttributeSwap() {
            super(AddonTemplate.CATEGORY, "attribute-swap", "Swaps maces based on fall distance and sword hits.");
        }

        @Override
        public void onActivate() {
            super.onActivate();
            if (!hasMaceInHotbar()) {
                warning("No mace found in hotbar. Deactivating AttributeSwap.");
                toggle();
            }
        }

        // Add a method to check if another module has taken action this tick
        private boolean hasAnotherModuleTakenAction() {
            // Check if MaceAura has taken action
            try {
                Class<?> maceAuraClass = Class.forName("com.example.addon.modules.MaceAura");
                java.lang.reflect.Field actionTakenField = maceAuraClass.getDeclaredField("actionTakenThisTick");
                actionTakenField.setAccessible(true);
                return actionTakenField.getBoolean(null);
            } catch (Exception e) {
                // If we can't check, assume no conflict
                return false;
            }
        }

        @EventHandler
        private void onTick(TickEvent.Pre event) {
            if (Modules.get().get(StunSlam.class).isActive()) return;
            if (autoDetectMaces.get()) {
                findMaces();
            }

            if (swapBackDelay > 0) {
                swapBackDelay--;
            } else if (swapBackDelay == 0) {
                InvUtils.swap(originalSlot, true);
                swapBackDelay = -1;
                originalSlot = -1;
            }
        }

        private boolean hasDensityEnchantment(ItemStack stack) {
            return EnchantmentUtils.hasEnchantment(stack, Enchantments.DENSITY);
        }

        private boolean hasBreachEnchantment(ItemStack stack) {
            return EnchantmentUtils.hasEnchantment(stack, Enchantments.BREACH);
        }

        private void findMaces() {
            if (mc.player == null) return;

            int foundDensityMaceSlot = -1;
            int foundBreachMaceSlot = -1;

            for (int i = 0; i < 9; i++) { // Iterate through hotbar slots
                ItemStack stack = mc.player.getInventory().getStack(i);
                if (stack.getItem() == Items.MACE) { // Check if it's a mace
                    if (hasDensityEnchantment(stack)) {
                        foundDensityMaceSlot = i;
                    } else if (hasBreachEnchantment(stack)) {
                        foundBreachMaceSlot = i;
                    }
                }
            }

            if (foundDensityMaceSlot != -1) {
                densityMaceSlot.set(foundDensityMaceSlot + 1);
            }
            if (foundBreachMaceSlot != -1) {
                breachMaceSlot.set(foundBreachMaceSlot + 1);
            }
        }

        @EventHandler
        private void onAttack(AttackEntityEvent event) {
            // Check if another module has already taken action this tick
            if (hasAnotherModuleTakenAction()) {
                info("Another module has already taken action this tick. Skipping AttributeSwap.");
                return;
            }
            
            if (Modules.get().get(StunSlam.class).isActive()) return;
            if (mc.player == null || originalSlot != -1) return;

            FindItemResult findItemResult = InvUtils.findInHotbar(mc.player.getMainHandStack().getItem());
            if (!findItemResult.found()) return;
            originalSlot = findItemResult.slot();

            if (mc.player.fallDistance >= minFallDistance.get()) {
                // Swap to density enchanted mace
                InvUtils.swap(densityMaceSlot.get() - 1, true);
            } else {
                // Swap to breach enchanted mace
                InvUtils.swap(breachMaceSlot.get() - 1, true);
            }
            swapBackDelay = 2;
            
            // Mark that this module is taking action this tick
            try {
                Class<?> maceAuraClass = Class.forName("com.example.addon.modules.MaceAura");
                java.lang.reflect.Field actionTakenField = maceAuraClass.getDeclaredField("actionTakenThisTick");
                actionTakenField.setAccessible(true);
                actionTakenField.setBoolean(null, true);
            } catch (Exception e) {
                // Ignore if we can't set the flag
            }
        }

        public static void swapToMace() {
            MinecraftClient mc = MinecraftClient.getInstance();
            AttributeSwap attributeSwap = Modules.get().get(AttributeSwap.class);
            if (attributeSwap.isActive() && mc.player != null) {
                InvUtils.swap(attributeSwap.breachMaceSlot.get() - 1, true);
                attributeSwap.swapBackDelay = 2;
                attributeSwap.originalSlot = InvUtils.findInHotbar(mc.player.getMainHandStack().getItem()).slot();
            }
        }

        private boolean hasMaceInHotbar() {
            for (int i = 0; i < 9; i++) {
                ItemStack stack = mc.player.getInventory().getStack(i);
                if (stack.getItem() == Items.MACE) {
                    return true;
                }
            }
            return false;
        }
    }