package com.example.addon.modules;

import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Categories;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.utils.player.FindItemResult;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.block.Blocks;
import net.minecraft.item.Items;
import net.minecraft.util.math.BlockPos;

public class AutoRespawnAnchor extends Module {
    private final SettingGroup sgGeneral = settings.getDefaultGroup();



    private final Setting<Integer> delay = sgGeneral.add(new IntSetting.Builder()
        .name("delay")
        .description("Delay between actions in ticks.")
        .defaultValue(4)
        .min(0)
        .sliderMax(20)
        .build()
    );

    private final Setting<Boolean> autoDisable = sgGeneral.add(new BoolSetting.Builder()
        .name("auto-disable")
        .description("Automatically disables after completing the sequence.")
        .defaultValue(false)
        .build()
    );

    private final Setting<Integer> detectionRange = sgGeneral.add(new IntSetting.Builder()
        .name("detection-range")
        .description("Range to detect respawn anchors.")
        .defaultValue(3)
        .min(1)
        .max(6)
        .sliderMax(6)
        .build()
    );



    private BlockPos anchorPos = null;
    private State currentState = State.WAITING_FOR_ANCHOR;
    private BlockPos lastProcessedAnchor = null;
    private long lastProcessTime = 0;



    public AutoRespawnAnchor() {
        super(Categories.Player, "auto-respawn-anchor", "Automatically sets up respawn anchors with glowstone and totem interaction.");
    }

    private enum State {
        WAITING_FOR_ANCHOR,
        PROCESSING_ANCHOR
    }

    @Override
    public void onActivate() {
        anchorPos = null;
        currentState = State.WAITING_FOR_ANCHOR;
        lastProcessedAnchor = null;
        lastProcessTime = 0;
    }

    @EventHandler
    private void onTick(TickEvent.Pre event) {
        if (mc.player == null || mc.world == null) return;

        switch (currentState) {
            case WAITING_FOR_ANCHOR -> {
                BlockPos newAnchor = findNearbyRespawnAnchor();
                long currentTime = System.currentTimeMillis();

                if (newAnchor != null &&
                    (!newAnchor.equals(lastProcessedAnchor) || currentTime - lastProcessTime > 1000)) {
                    anchorPos = newAnchor;
                    lastProcessedAnchor = newAnchor;
                    lastProcessTime = currentTime;
                    currentState = State.PROCESSING_ANCHOR;
                    info("New respawn anchor detected at " + anchorPos.toShortString() + " - processing immediately");

                    // Process immediately without delay
                    processAnchor();
                }
            }
            case PROCESSING_ANCHOR -> {
                // Reset to waiting state immediately after processing
                currentState = State.WAITING_FOR_ANCHOR;
                anchorPos = null;
            }
        }
    }

    private void processAnchor() {
        // Step 1: Swap to glowstone
        FindItemResult glowstone = InvUtils.findInHotbar(Items.GLOWSTONE);
        if (glowstone.found()) {
            InvUtils.swap(glowstone.slot(), false);
        }

        // Step 2: Right-click (place glowstone)
        InputSimulator.simulateRightClickPress();

        // Step 3: Swap to totem
        FindItemResult totem = InvUtils.findInHotbar(Items.TOTEM_OF_UNDYING);
        if (totem.found()) {
            InvUtils.swap(totem.slot(), false);
        }

        // Step 4: Right-click (use totem on anchor)
        InputSimulator.simulateRightClickPress();

        info("Anchor macro executed");
    }





    private BlockPos findNearbyRespawnAnchor() {
        if (mc.player == null || mc.world == null) return null;

        BlockPos playerPos = mc.player.getBlockPos();
        int range = detectionRange.get();

        for (int x = -range; x <= range; x++) {
            for (int y = -range; y <= range; y++) {
                for (int z = -range; z <= range; z++) {
                    BlockPos pos = playerPos.add(x, y, z);
                    if (mc.world.getBlockState(pos).getBlock() == Blocks.RESPAWN_ANCHOR) {
                        // Return ANY respawn anchor, regardless of charge level
                        return pos;
                    }
                }
            }
        }
        return null;
    }

    @Override
    public String getInfoString() {
        return currentState.toString().toLowerCase().replace("_", " ");
    }
}