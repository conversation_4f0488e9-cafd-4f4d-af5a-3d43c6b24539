package com.example.addon.modules;

import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Categories;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.utils.player.FindItemResult;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import net.minecraft.item.Items;

public class AutoRespawnAnchor extends Module {
    private final SettingGroup sgGeneral = settings.getDefaultGroup();



    private final Setting<Integer> delay = sgGeneral.add(new IntSetting.Builder()
        .name("delay")
        .description("Delay between actions in ticks.")
        .defaultValue(4)
        .min(0)
        .sliderMax(20)
        .build()
    );

    private final Setting<Boolean> autoDisable = sgGeneral.add(new BoolSetting.Builder()
        .name("auto-disable")
        .description("Automatically disables after completing the sequence.")
        .defaultValue(false)
        .build()
    );









    public AutoRespawnAnchor() {
        super(Categories.Player, "auto-respawn-anchor", "Automatically sets up respawn anchors with glowstone and totem interaction.");
    }



    @Override
    public void onActivate() {
        // Execute the macro immediately when activated
        processAnchor();

        // Auto-disable after execution if enabled
        if (autoDisable.get()) {
            toggle();
        }
    }



    private void processAnchor() {
        // Step 1: Swap to glowstone
        FindItemResult glowstone = InvUtils.findInHotbar(Items.GLOWSTONE);
        if (glowstone.found()) {
            InvUtils.swap(glowstone.slot(), false);
        }

        // Step 2: Right-click (place glowstone)
        InputSimulator.simulateRightClickPress();

        // Step 3: Swap to totem
        FindItemResult totem = InvUtils.findInHotbar(Items.TOTEM_OF_UNDYING);
        if (totem.found()) {
            InvUtils.swap(totem.slot(), false);
        }

        // Step 4: Right-click (use totem on anchor)
        InputSimulator.simulateRightClickPress();

        info("Anchor macro executed");
    }






}