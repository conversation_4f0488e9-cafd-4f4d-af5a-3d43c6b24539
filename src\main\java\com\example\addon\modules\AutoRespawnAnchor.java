package com.example.addon.modules;

import meteordevelopment.meteorclient.events.entity.player.InteractBlockEvent;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Categories;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.utils.player.FindItemResult;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.block.Blocks;
import net.minecraft.item.Items;

public class AutoRespawnAnchor extends Module {
    private final SettingGroup sgGeneral = settings.getDefaultGroup();

    private enum MacroState {
        IDLE,
        SWAP_TO_GLOWSTONE,
        PLACE_GLOWSTONE,
        SWAP_TO_TOTEM,
        USE_TOTEM
    }

    private MacroState currentState = MacroState.IDLE;



    private final Setting<Integer> delay = sgGeneral.add(new IntSetting.Builder()
        .name("delay")
        .description("Delay between actions in ticks.")
        .defaultValue(4)
        .min(0)
        .sliderMax(20)
        .build()
    );

    private final Setting<Boolean> autoDisable = sgGeneral.add(new BoolSetting.Builder()
        .name("auto-disable")
        .description("Automatically disables after completing the sequence.")
        .defaultValue(false)
        .build()
    );









    public AutoRespawnAnchor() {
        super(Categories.Player, "auto-respawn-anchor", "Automatically sets up respawn anchors with glowstone and totem interaction.");
    }



    @EventHandler
    private void onInteractBlock(InteractBlockEvent event) {
        if (mc.player == null || mc.world == null) return;

        // Check if player is holding a respawn anchor and macro is not already running
        if (currentState == MacroState.IDLE &&
            (mc.player.getMainHandStack().getItem() == Items.RESPAWN_ANCHOR ||
             mc.player.getOffHandStack().getItem() == Items.RESPAWN_ANCHOR)) {

            info("Respawn anchor placement detected - starting macro");

            // Start the macro sequence
            currentState = MacroState.SWAP_TO_GLOWSTONE;
        }
    }

    @EventHandler
    private void onTick(TickEvent.Pre event) {
        if (mc.player == null || mc.world == null) return;

        switch (currentState) {
            case SWAP_TO_GLOWSTONE -> {
                FindItemResult glowstone = InvUtils.findInHotbar(Items.GLOWSTONE);
                if (glowstone.found()) {
                    InvUtils.swap(glowstone.slot(), false);
                }
                currentState = MacroState.PLACE_GLOWSTONE;
            }
            case PLACE_GLOWSTONE -> {
                InputSimulator.simulateRightClickPress();
                currentState = MacroState.SWAP_TO_TOTEM;
            }
            case SWAP_TO_TOTEM -> {
                FindItemResult totem = InvUtils.findInHotbar(Items.TOTEM_OF_UNDYING);
                if (totem.found()) {
                    InvUtils.swap(totem.slot(), false);
                }
                currentState = MacroState.USE_TOTEM;
            }
            case USE_TOTEM -> {
                InputSimulator.simulateRightClickPress();
                currentState = MacroState.IDLE;
                info("Anchor macro completed");

                // Auto-disable after execution if enabled
                if (autoDisable.get()) {
                    toggle();
                }
            }
        }
    }










}