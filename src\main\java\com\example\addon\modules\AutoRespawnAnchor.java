package com.example.addon.modules;

import com.example.addon.utils.SmoothAimingUtils;
import meteordevelopment.meteorclient.events.render.Render3DEvent;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Categories;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.utils.player.FindItemResult;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.block.Blocks;
import net.minecraft.item.Items;
import net.minecraft.util.ActionResult;
import net.minecraft.util.Hand;
import net.minecraft.util.hit.BlockHitResult;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Box;
import net.minecraft.util.math.Direction;
import net.minecraft.util.math.Vec3d;
import java.util.Random;

public class AutoRespawnAnchor extends Module {
    private final SettingGroup sgGeneral = settings.getDefaultGroup();
    private final SettingGroup sgAiming = settings.createGroup("Aiming");

    private enum InteractionType {
        Packet,
        RightClickSimulation
    }

    private final Setting<InteractionType> interactionType = sgGeneral.add(new EnumSetting.Builder<InteractionType>()
        .name("interaction-type")
        .description("The method used to interact with the respawn anchor.")
        .defaultValue(InteractionType.RightClickSimulation)
        .build()
    );

    private final Setting<Integer> delay = sgGeneral.add(new IntSetting.Builder()
        .name("delay")
        .description("Delay between actions in ticks.")
        .defaultValue(4)
        .min(0)
        .sliderMax(20)
        .build()
    );

    private final Setting<Boolean> autoDisable = sgGeneral.add(new BoolSetting.Builder()
        .name("auto-disable")
        .description("Automatically disables after completing the sequence.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Double> aimSpeed = sgAiming.add(new DoubleSetting.Builder()
        .name("aim-speed")
        .description("Speed of aiming towards the anchor.")
        .defaultValue(7.5)
        .min(0.25)
        .max(25.0)
        .sliderMax(25.0)
        .visible(() -> interactionType.get() == InteractionType.Packet && doAim.get())
        .build()
    );

    private final Setting<Double> smoothingFactor = sgAiming.add(new DoubleSetting.Builder()
        .name("smoothing-factor")
        .description("Smoothing factor for aiming (higher = smoother).")
        .defaultValue(0.2)
        .min(0.0)
        .max(1.0)
        .sliderMax(1.0)
        .visible(() -> interactionType.get() == InteractionType.Packet && doAim.get())
        .build()
    );

    private final Setting<Double> randomization = sgAiming.add(new DoubleSetting.Builder()
        .name("randomization")
        .description("Amount of randomization in aiming.")
        .defaultValue(0.1)
        .min(0.0)
        .max(1.0)
        .sliderMax(1.0)
        .visible(() -> interactionType.get() == InteractionType.Packet && doAim.get())
        .build()
    );

    private final Setting<Boolean> doAim = sgAiming.add(new BoolSetting.Builder()
        .name("do-aim")
        .description("Whether to aim at the anchor.")
        .defaultValue(false)
        .visible(() -> interactionType.get() == InteractionType.Packet)
        .build()
    );

    private final Setting<Boolean> exponentialSmoothing = sgAiming.add(new BoolSetting.Builder()
        .name("exponential-smoothing")
        .description("Use exponential smoothing for even smoother aiming.")
        .defaultValue(false)
        .visible(() -> interactionType.get() == InteractionType.Packet && doAim.get())
        .build()
    );

    private final Setting<Double> aimingVariation = sgAiming.add(new DoubleSetting.Builder()
        .name("aiming-variation")
        .description("How much to vary from the closest point (0.0 = exact closest, 1.0 = anywhere on block).")
        .defaultValue(0.3)
        .min(0.0)
        .max(1.0)
        .sliderMax(1.0)
        .visible(() -> interactionType.get() == InteractionType.Packet && doAim.get())
        .build()
    );

    private BlockPos anchorPos = null;
    private int tickCounter = 0;
    private State currentState = State.IDLE;

    private Vec3d targetPos = null;
    private long lastFrameTime = System.nanoTime();
    private final Random random = new Random();

    public AutoRespawnAnchor() {
        super(Categories.Player, "auto-respawn-anchor", "Automatically sets up respawn anchors with glowstone and totem interaction.");
    }

    private enum State {
        IDLE,
        WAIT_FOR_ANCHOR_PLACE,
        SWAP_TO_GLOWSTONE,
        AIM_FOR_GLOWSTONE,
        PLACE_GLOWSTONE,
        WAIT_AFTER_GLOWSTONE,
        SWAP_TO_TOTEM,
        WAIT_FOR_TOTEM_SWAP,
        AIM_FOR_TOTEM,
        RIGHT_CLICK_ANCHOR,
        COMPLETE
    }

    @Override
    public void onActivate() {
        anchorPos = null;
        currentState = State.WAIT_FOR_ANCHOR_PLACE;
        tickCounter = 0;
        lastFrameTime = System.nanoTime();
    }

    @EventHandler
    private void onTick(TickEvent.Pre event) {
        if (mc.player == null || mc.world == null) return;

        tickCounter++;

        switch (currentState) {
            case WAIT_FOR_ANCHOR_PLACE -> {
                BlockPos newAnchor = findNearbyRespawnAnchor();
                if (newAnchor != null) {
                    anchorPos = newAnchor;
                    currentState = State.SWAP_TO_GLOWSTONE;
                    tickCounter = 0;
                    info("Respawn anchor detected at " + anchorPos.toShortString());
                }
            }
            case SWAP_TO_GLOWSTONE -> {
                if (tickCounter >= delay.get()) {
                    FindItemResult glowstone = InvUtils.findInHotbar(Items.GLOWSTONE);
                    if (!glowstone.found()) {
                        error("No glowstone found in hotbar!");
                        if (autoDisable.get()) toggle();
                        return;
                    }
                    InvUtils.swap(glowstone.slot(), false);
                    if (interactionType.get() == InteractionType.Packet && doAim.get()) {
                        currentState = State.AIM_FOR_GLOWSTONE;
                        targetPos = calculateOptimalAimPoint();
                    } else {
                        currentState = State.PLACE_GLOWSTONE;
                    }
                    tickCounter = 0;
                }
            }
            case AIM_FOR_GLOWSTONE -> {
                if (tickCounter >= delay.get()) {
                    currentState = State.PLACE_GLOWSTONE;
                    tickCounter = 0;
                }
            }
            case PLACE_GLOWSTONE -> {
                if (tickCounter >= delay.get() && anchorPos != null) {
                    if (placeGlowstoneOnAnchor()) {
                        currentState = State.WAIT_AFTER_GLOWSTONE;
                        tickCounter = 0;
                    }
                }
            }
            case WAIT_AFTER_GLOWSTONE -> {
                if (tickCounter >= delay.get()) {
                    currentState = State.SWAP_TO_TOTEM;
                    tickCounter = 0;
                }
            }
            case SWAP_TO_TOTEM -> {
                if (tickCounter >= delay.get()) {
                    if (mc.player.getMainHandStack().getItem() == Items.TOTEM_OF_UNDYING) {
                        if (interactionType.get() == InteractionType.Packet && doAim.get()) {
                            currentState = State.AIM_FOR_TOTEM;
                            targetPos = calculateOptimalAimPoint();
                        } else {
                            currentState = State.RIGHT_CLICK_ANCHOR;
                        }
                        tickCounter = 0;
                        return;
                    }

                    FindItemResult totemInHotbar = InvUtils.findInHotbar(Items.TOTEM_OF_UNDYING);
                    if (totemInHotbar.found()) {
                        info("Found totem in hotbar, swapping to main hand.");
                        InvUtils.swap(totemInHotbar.slot(), false);
                        currentState = State.WAIT_FOR_TOTEM_SWAP;
                        tickCounter = 0;
                        return;
                    }

                    error("No Totem of Undying found in your hotbar.");
                    if (autoDisable.get()) {
                        toggle();
                    }
                }
            }
            case WAIT_FOR_TOTEM_SWAP -> {
                if (mc.player.getMainHandStack().getItem() == Items.TOTEM_OF_UNDYING) {
                    if (interactionType.get() == InteractionType.Packet && doAim.get()) {
                        currentState = State.AIM_FOR_TOTEM;
                        targetPos = calculateOptimalAimPoint();
                    } else {
                        currentState = State.RIGHT_CLICK_ANCHOR;
                    }
                    tickCounter = 0;
                } else if (tickCounter > 40) {
                    error("Failed to swap to totem in time. Aborting.");
                    if (autoDisable.get()) toggle();
                }
            }
            case AIM_FOR_TOTEM -> {
                if (tickCounter >= delay.get()) {
                    currentState = State.RIGHT_CLICK_ANCHOR;
                    tickCounter = 0;
                }
            }
            case RIGHT_CLICK_ANCHOR -> {
                if (tickCounter >= delay.get() && anchorPos != null) {
                    if (rightClickAnchorWithTotem()) {
                        currentState = State.COMPLETE;
                        tickCounter = 0;
                    }
                }
            }
            case COMPLETE -> {
                if (autoDisable.get()) {
                    info("Respawn anchor setup complete!");
                    toggle();
                } else {
                    currentState = State.WAIT_FOR_ANCHOR_PLACE;
                    anchorPos = null;
                }
            }
        }
    }

    @EventHandler
    private void onRender(Render3DEvent event) {
        if (targetPos != null && interactionType.get() == InteractionType.Packet && doAim.get()) {
            updateSmoothAiming((System.nanoTime() - lastFrameTime) / 1_000_000_000.0f);
            lastFrameTime = System.nanoTime();
        }
    }

    private Vec3d calculateOptimalAimPoint() {
        if (anchorPos == null || mc.player == null) return null;

        Vec3d playerEyePos = mc.player.getEyePos();
        Box blockBox = new Box(anchorPos);
        Vec3d closestPoint = getClosestPointOnBox(playerEyePos, blockBox);

        double variation = aimingVariation.get();
        if (variation > 0.0) {
            double maxOffset = variation * 0.3;
            double offsetX = (random.nextDouble() - 0.5) * maxOffset;
            double offsetY = (random.nextDouble() - 0.5) * maxOffset;
            double offsetZ = (random.nextDouble() - 0.5) * maxOffset;

            Vec3d variedPoint = closestPoint.add(offsetX, offsetY, offsetZ);

            double minX = anchorPos.getX();
            double maxX = anchorPos.getX() + 1.0;
            double minY = anchorPos.getY();
            double maxY = anchorPos.getY() + 1.0;
            double minZ = anchorPos.getZ();
            double maxZ = anchorPos.getZ() + 1.0;

            variedPoint = new Vec3d(
                Math.max(minX + 0.1, Math.min(maxX - 0.1, variedPoint.x)),
                Math.max(minY + 0.1, Math.min(maxY - 0.1, variedPoint.y)),
                Math.max(minZ + 0.1, Math.min(maxZ - 0.1, variedPoint.z))
            );

            return variedPoint;
        }

        return closestPoint;
    }

    private Vec3d getClosestPointOnBox(Vec3d point, Box box) {
        double clampedX = Math.max(box.minX, Math.min(box.maxX, point.x));
        double clampedY = Math.max(box.minY, Math.min(box.maxY, point.y));
        double clampedZ = Math.max(box.minZ, Math.min(box.maxZ, point.z));
        return new Vec3d(clampedX, clampedY, clampedZ);
    }

    private void updateSmoothAiming(float frameTime) {
        if (mc.player == null || targetPos == null) return;

        Vec3d playerPos = mc.player.getEyePos();
        float currentYaw = mc.player.getYaw();
        float currentPitch = mc.player.getPitch();
        double scaledAimSpeed = aimSpeed.get();
        float[] newRotations;

        if (exponentialSmoothing.get()) {
            newRotations = SmoothAimingUtils.calculateExponentialSmoothRotations(
                currentYaw, currentPitch, targetPos, playerPos,
                scaledAimSpeed, frameTime, smoothingFactor.get()
            );
        } else {
            newRotations = SmoothAimingUtils.calculateSmoothRotations(
                currentYaw, currentPitch, targetPos, playerPos,
                scaledAimSpeed, frameTime, smoothingFactor.get()
            );
        }

        if (randomization.get() > 0.0) {
            newRotations = SmoothAimingUtils.addRandomizationToRotations(
                newRotations, randomization.get()
            );
        }

        mc.player.setYaw(newRotations[0]);
        mc.player.setPitch(newRotations[1]);
    }

    private boolean placeGlowstoneOnAnchor() {
        if (mc.player == null || mc.world == null || anchorPos == null) return false;
        if (mc.world.getBlockState(anchorPos).getBlock() != Blocks.RESPAWN_ANCHOR) {
            error("Respawn anchor no longer exists!");
            return false;
        }

        if (interactionType.get() == InteractionType.RightClickSimulation) {
            InputSimulator.simulateRightClickPress();
            info("Placed glowstone in anchor using right-click simulation");
            return true;
        } else {
            Vec3d interactionPos = targetPos != null ? targetPos : Vec3d.ofCenter(anchorPos);
            BlockHitResult hitResult = new BlockHitResult(interactionPos, Direction.UP, anchorPos, false);
            ActionResult result = mc.interactionManager.interactBlock(mc.player, Hand.MAIN_HAND, hitResult);

            if (result.isAccepted()) {
                info("Placed glowstone in anchor");
                mc.player.swingHand(Hand.MAIN_HAND);
                return true;
            } else {
                warning("Failed to place glowstone, retrying...");
                return false;
            }
        }
    }

    private boolean rightClickAnchorWithTotem() {
        if (mc.player == null || mc.world == null || anchorPos == null) return false;
        if (mc.world.getBlockState(anchorPos).getBlock() != Blocks.RESPAWN_ANCHOR) {
            error("Respawn anchor no longer exists!");
            return false;
        }

        if (interactionType.get() == InteractionType.RightClickSimulation) {
            InputSimulator.simulateRightClickPress();
            info("Right-clicked anchor with totem using right-click simulation");
            return true;
        } else {
            Vec3d interactionPos = targetPos != null ? targetPos : Vec3d.ofCenter(anchorPos);
            BlockHitResult hitResult = new BlockHitResult(interactionPos, Direction.UP, anchorPos, false);
            ActionResult result = mc.interactionManager.interactBlock(mc.player, Hand.MAIN_HAND, hitResult);

            if (result.isAccepted()) {
                info("Right-clicked anchor with totem");
                mc.player.swingHand(Hand.MAIN_HAND);
                return true;
            } else {
                warning("Failed to right-click anchor with totem, retrying...");
                return false;
            }
        }
    }

    private BlockPos findNearbyRespawnAnchor() {
        if (mc.player == null || mc.world == null) return null;

        BlockPos playerPos = mc.player.getBlockPos();
        for (int x = -2; x <= 2; x++) {
            for (int y = -2; y <= 2; y++) {
                for (int z = -2; z <= 2; z++) {
                    BlockPos pos = playerPos.add(x, y, z);
                    if (mc.world.getBlockState(pos).getBlock() == Blocks.RESPAWN_ANCHOR) {
                        int charge = mc.world.getBlockState(pos).get(net.minecraft.block.RespawnAnchorBlock.CHARGES);
                        if (charge == 0) {
                            return pos;
                        }
                    }
                }
            }
        }
        return null;
    }

    @Override
    public String getInfoString() {
        return currentState.toString().toLowerCase().replace("_", " ");
    }
}