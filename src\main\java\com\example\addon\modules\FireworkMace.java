package com.example.addon.modules;

import com.example.addon.AddonTemplate;
import meteordevelopment.meteorclient.events.render.Render3DEvent;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.utils.entity.TargetUtils;
import meteordevelopment.meteorclient.utils.entity.SortPriority;
import meteordevelopment.meteorclient.systems.modules.Modules;
import net.minecraft.entity.Entity;
import net.minecraft.entity.LivingEntity;
import java.util.function.Predicate;
import meteordevelopment.meteorclient.utils.player.FindItemResult;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.entity.EquipmentSlot;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import net.minecraft.network.packet.c2s.play.ClientCommandC2SPacket;
import net.minecraft.util.Hand;
import net.minecraft.util.math.Vec3d;
import net.minecraft.util.math.Box;
import net.minecraft.util.math.BlockPos;

import org.lwjgl.glfw.GLFW;
import net.minecraft.client.option.KeyBinding;

public class FireworkMace extends Module {
    private final SettingGroup sgGeneral = settings.getDefaultGroup();
    private LivingEntity target = null;
    private Predicate<Entity> targetAcquisitionPredicate;
    private Predicate<Entity> targetValidationPredicate;
    private int step = 0;
    private int stepTimer = 0;
    private long lastActionTime = 0;
    private double playerStartHeight = 0;
    private boolean elytraSwapTriggered = false;
    private long glideFailCooldown = 0;

    private float targetPitchOverride = Float.NaN;

    private final Setting<Boolean> holdKey = sgGeneral.add(new BoolSetting.Builder().name("hold-key").description("Only activates when a key is held down.").defaultValue(false).build());
    private final Setting<Double> targetAcquireRange = sgGeneral.add(new DoubleSetting.Builder().name("target-acquire-range").description("The range to initially acquire targets.").defaultValue(10.0).min(1.0).sliderMax(20.0).build());
    private final Setting<Double> maxTrackingRange = sgGeneral.add(new DoubleSetting.Builder().name("max-tracking-range").description("Maximum range to track a target before dropping it.").defaultValue(50.0).min(10.0).sliderMax(100.0).build());
    private final Setting<Integer> delay = sgGeneral.add(new IntSetting.Builder().name("delay").description("Delay between actions in ticks.").defaultValue(1).min(0).sliderMax(20).build());

    private final Setting<Double> initialAscentHeight = sgGeneral.add(new DoubleSetting.Builder().name("initial-ascent-height").description("Height above player's starting Y to reach during initial rocket ascent.").defaultValue(15.0).min(5.0).max(50.0).build());
    private final Setting<Double> earlyGlideHeight = sgGeneral.add(new DoubleSetting.Builder().name("early-glide-height").description("Minimum height above player's start Y to attempt gliding (after jump).").defaultValue(0.5).min(0.1).max(2.0).build());
    private final Setting<Integer> initialRocketDelay = sgGeneral.add(new IntSetting.Builder().name("initial-rocket-delay").description("Ticks delay after starting glide before the very first rocket.").defaultValue(2).min(0).max(10).build());
    private final Setting<Integer> reEquipElytraDelay = sgGeneral.add(new IntSetting.Builder().name("re-equip-elytra-delay").description("Ticks delay after taking off elytra mid-air to re-equip it.").defaultValue(2).min(1).max(10).build());
    private final Setting<Integer> elytraSwapTimeout = sgGeneral.add(new IntSetting.Builder().name("elytra-swap-timeout").description("Maximum ticks to wait for elytra swap operations to complete.").defaultValue(40).min(20).max(100).build());
    private final Setting<Double> swapDelay = sgGeneral.add(new DoubleSetting.Builder()
        .name("swap-delay")
        .description("Delay in ticks after swapping to the target item.")
        .defaultValue(0.0)
        .min(0.0)
        .sliderMin(0.0)
        .build()
    );
    private final Setting<Double> interactDelay = sgGeneral.add(new DoubleSetting.Builder()
        .name("interact-delay")
        .description("Delay in ticks after interacting with the item.")
        .defaultValue(0.0)
        .min(0.0)
        .sliderMin(0.0)
        .build()
    );
    private final Setting<Double> swapBackDelaySetting = sgGeneral.add(new DoubleSetting.Builder()
        .name("swap-back-delay")
        .description("Delay in ticks before swapping back to the original slot.")
        .defaultValue(0.0)
        .min(0.0)
        .sliderMin(0.0)
        .build()
    );
    private final Setting<Double> aimRange = sgGeneral.add(new DoubleSetting.Builder().name("aim-range").description("Maximum distance to aim at targets.").defaultValue(20.0).min(5.0).max(50.0).build());

    private final Setting<Boolean> smoothAim = sgGeneral.add(new BoolSetting.Builder()
        .name("smooth-aim")
        .description("Should aim smoothly or instantly.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Double> aimSpeed = sgGeneral.add(new DoubleSetting.Builder()
        .name("aim-speed")
        .description("Speed of aiming adjustment.")
        .defaultValue(0.5)
        .min(0.1)
        .max(1.0)
        .visible(smoothAim::get)
        .build()
    );

    private final Setting<Double> attackDistance = sgGeneral.add(new DoubleSetting.Builder().name("attack-distance").description("Distance to target before disengaging elytra and attacking.").defaultValue(8.0).min(1.0).max(20.0).build());
    private final Setting<Integer> fireworkInterval = sgGeneral.add(new IntSetting.Builder().name("firework-interval").description("Ticks between firework usage while ascending/gliding.").defaultValue(20).min(5).max(60).build());

    // Internal ElytraSwap state variables
    private int elytraSwapTimer = -1;
    private int elytraSwapState = 0;
    private FindItemResult elytraSwapTargetItem;
    private int elytraSwapOriginalHotbarSlot = -1;
    private boolean elytraSwapWasRmbDown;

    public FireworkMace() {
        super(AddonTemplate.CATEGORY, "firework-mace", "Automatically uses firework with mace for combat.");
    }

    @Override
    public void onActivate() {
        super.onActivate();
        targetAcquisitionPredicate = entity -> {
            if (!(entity instanceof LivingEntity)) return false;
            LivingEntity livingEntity = (LivingEntity) entity;
            return livingEntity != mc.player && livingEntity.isAlive() && !livingEntity.isDead() && mc.player.distanceTo(livingEntity) <= targetAcquireRange.get();
        };
        targetValidationPredicate = entity -> {
            if (!(entity instanceof LivingEntity)) return false;
            LivingEntity livingEntity = (LivingEntity) entity;
            return livingEntity != mc.player && livingEntity.isAlive() && !livingEntity.isDead() && mc.player.distanceTo(livingEntity) <= maxTrackingRange.get();
        };

        step = 0;
        stepTimer = 0;
        targetPitchOverride = Float.NaN;
        playerStartHeight = mc.player.getY();
        elytraSwapTriggered = false;
        
        // Reset internal ElytraSwap state
        elytraSwapTimer = -1;
        elytraSwapState = 0;
        elytraSwapTargetItem = null;
        elytraSwapOriginalHotbarSlot = -1;
        glideFailCooldown = 0;
        
        findNewTarget();
    }

    @Override
    public void onDeactivate() {
        super.onDeactivate();
        targetPitchOverride = Float.NaN;
        elytraSwapTriggered = false;
        
        // Reset internal ElytraSwap state
        elytraSwapTimer = -1;
        elytraSwapState = 0;
        elytraSwapTargetItem = null;
        elytraSwapOriginalHotbarSlot = -1;
        
        if (mc.player != null && mc.player.getEquippedStack(EquipmentSlot.CHEST).getItem() == Items.ELYTRA) {
            // No need to toggle external module anymore
        }
    }

    private void findNewTarget() {
        LivingEntity newTarget = (LivingEntity) TargetUtils.get(targetAcquisitionPredicate, SortPriority.LowestDistance);

        if (newTarget != null && (target == null || !newTarget.equals(target))) {
            info("Target acquired: " + newTarget.getName().getString());
            target = newTarget;
        }
    }

    private boolean isTargetValid() {
        if (target == null || target.isDead() || !target.isAlive()) {
            return false;
        }
        return targetValidationPredicate.test(target);
    }

    private void setElytraState(boolean shouldBeWearingElytra) {
        boolean isWearingElytra = mc.player.getEquippedStack(EquipmentSlot.CHEST).getItem() == Items.ELYTRA;

        if (shouldBeWearingElytra == isWearingElytra) {
            return; // Already in desired state
        }

        if (elytraSwapTriggered || elytraSwapState != 0) {
            return; // Already processing a swap
        }

        // Initialize internal ElytraSwap process
        elytraSwapWasRmbDown = GLFW.glfwGetMouseButton(mc.getWindow().getHandle(), GLFW.GLFW_MOUSE_BUTTON_2) == GLFW.GLFW_PRESS;
        if (elytraSwapWasRmbDown) {
            KeyBinding.setKeyPressed(mc.options.useKey.getDefaultKey(), false);
        }

        ItemStack equippedChest = mc.player.getInventory().getStack(38); // 38 is the chest slot

        if (equippedChest.getItem() == Items.ELYTRA) {
            // Elytra is equipped, look for a chestplate
            elytraSwapTargetItem = InvUtils.findInHotbar(itemStack -> itemStack.getItem() == Items.DIAMOND_CHESTPLATE ||
                itemStack.getItem() == Items.NETHERITE_CHESTPLATE ||
                itemStack.getItem() == Items.IRON_CHESTPLATE ||
                itemStack.getItem() == Items.CHAINMAIL_CHESTPLATE ||
                itemStack.getItem() == Items.GOLDEN_CHESTPLATE ||
                itemStack.getItem() == Items.LEATHER_CHESTPLATE);
        } else if (equippedChest.getItem() == Items.DIAMOND_CHESTPLATE ||
                   equippedChest.getItem() == Items.NETHERITE_CHESTPLATE ||
                   equippedChest.getItem() == Items.IRON_CHESTPLATE ||
                   equippedChest.getItem() == Items.CHAINMAIL_CHESTPLATE ||
                   equippedChest.getItem() == Items.GOLDEN_CHESTPLATE ||
                   equippedChest.getItem() == Items.LEATHER_CHESTPLATE) {
            // Chestplate is equipped, look for an elytra
            elytraSwapTargetItem = InvUtils.findInHotbar(Items.ELYTRA);
        } else {
            // Nothing relevant equipped, prioritize elytra then chestplate
            elytraSwapTargetItem = InvUtils.findInHotbar(Items.ELYTRA);
            if (!elytraSwapTargetItem.found()) {
                elytraSwapTargetItem = InvUtils.findInHotbar(itemStack -> itemStack.getItem() == Items.DIAMOND_CHESTPLATE ||
                    itemStack.getItem() == Items.NETHERITE_CHESTPLATE ||
                    itemStack.getItem() == Items.IRON_CHESTPLATE ||
                    itemStack.getItem() == Items.CHAINMAIL_CHESTPLATE ||
                    itemStack.getItem() == Items.GOLDEN_CHESTPLATE ||
                    itemStack.getItem() == Items.LEATHER_CHESTPLATE);
            }
        }

        if (elytraSwapTargetItem.found()) {
            FindItemResult originalSlotResult = InvUtils.findInHotbar(mc.player.getMainHandStack().getItem());
            elytraSwapOriginalHotbarSlot = originalSlotResult.found() ? originalSlotResult.slot() : -1;
            elytraSwapState = 1;
            elytraSwapTimer = swapDelay.get().intValue();
            elytraSwapTriggered = true;
            info("Starting internal elytra swap process...");
        } else {
            warning("No suitable item found in hotbar for elytra swap.");
        }
    }



    private boolean equipMace() {
        FindItemResult mace = InvUtils.findInHotbar(Items.MACE);
        if (!mace.found()) {
            error("No mace found in hotbar! Disabling.");
            toggle();
            return false;
        }
        InvUtils.swap(mace.slot(), false);
        return true;
    }

    private void useFirework() {
        FindItemResult firework = InvUtils.findInHotbar(Items.FIREWORK_ROCKET);
        if (!firework.found()) {
            error("No firework rockets found in hotbar! Disabling.");
            toggle();
            return;
        }
        InvUtils.swap(firework.slot(), false);
        mc.interactionManager.interactItem(mc.player, Hand.MAIN_HAND);
    }

    private void startElytraFlight() {
        if (!mc.player.isGliding() && !mc.player.isOnGround()) {
            mc.getNetworkHandler().sendPacket(new ClientCommandC2SPacket(mc.player, ClientCommandC2SPacket.Mode.START_FALL_FLYING));
        }
    }

    private void attack() {
        if (target != null && mc.player.distanceTo(target) <= 6) {
            mc.interactionManager.attackEntity(mc.player, target);
        }
    }

    @EventHandler
    private void onTick(TickEvent.Pre event) {
        // Process internal ElytraSwap state machine
        if (elytraSwapState != 0) {
            if (elytraSwapTimer > 0) {
                elytraSwapTimer--;
            } else {
                if (elytraSwapState == 1) {
                    // Swap to target item
                    InvUtils.swap(elytraSwapTargetItem.slot(), false);
                    elytraSwapState = 2;
                    elytraSwapTimer = interactDelay.get().intValue();
                } else if (elytraSwapState == 2) {
                    // Interact with item (equip) - send packet directly to avoid shield auto-raise
                    mc.interactionManager.interactItem(mc.player, Hand.MAIN_HAND);
                    elytraSwapState = 3;
                    elytraSwapTimer = swapBackDelaySetting.get().intValue();
                } else if (elytraSwapState == 3) {
                    // Swap back to original slot
                    InvUtils.swap(elytraSwapOriginalHotbarSlot, false);
                    elytraSwapState = 0;
                    elytraSwapTimer = -1;
                    elytraSwapOriginalHotbarSlot = -1;
                    elytraSwapTriggered = false;
                    if (elytraSwapWasRmbDown && GLFW.glfwGetMouseButton(mc.getWindow().getHandle(), GLFW.GLFW_MOUSE_BUTTON_2) == GLFW.GLFW_PRESS) {
                        KeyBinding.setKeyPressed(mc.options.useKey.getDefaultKey(), true);
                    }
                }
            }
        }

        if (System.currentTimeMillis() - lastActionTime < delay.get() * 50) {
            return;
        }
        if (System.currentTimeMillis() < glideFailCooldown) {
            return;
        }
        lastActionTime = System.currentTimeMillis();

        if (holdKey.get() && !mc.options.attackKey.isPressed()) {
            info("Hold key released, toggling off.");
            toggle();
            return;
        }

        if (!isTargetValid()) {
            if (target != null) {
                info("Current target lost or out of max tracking range. Searching for a new one.");
            }
            target = null;
            findNewTarget();
            if (target == null) {
                step = 0;
                targetPitchOverride = Float.NaN;
                return;
            }
            step = 0;
            stepTimer = 0;
            targetPitchOverride = Float.NaN;
        }

        stepTimer++;

        double currentHeightAboveStart = mc.player.getY() - playerStartHeight;

        switch(step) {
            case 0: // Ensure elytra is on
                targetPitchOverride = Float.NaN;
                setElytraState(true);
                if (mc.player.getEquippedStack(EquipmentSlot.CHEST).getItem() == Items.ELYTRA) {
                    step = 1;
                    stepTimer = 0;
                    playerStartHeight = mc.player.getY();
                    elytraSwapTriggered = false;
                    info("Elytra equipped, starting jump sequence.");
                }
                break;

            case 1: // Jump and attempt early gliding
                if (mc.player.isOnGround()) {
                    mc.player.jump();
                    stepTimer = 0;
                    playerStartHeight = mc.player.getY();
                }
                targetPitchOverride = -90f;

                if (!mc.player.isOnGround() && mc.player.getY() > playerStartHeight + earlyGlideHeight.get() && !mc.player.isGliding()) {
                    startElytraFlight();
                    info("Attempting early glide at " + (mc.player.getY() - playerStartHeight) + " blocks above jump.");
                }

                if (mc.player.isGliding()) {
                    step = 2;
                    stepTimer = 0;
                    info("Gliding confirmed, preparing initial rocket.");
                } else if (stepTimer > 40) {
                    info("Failed to start early gliding after 40 ticks, restarting from step 0.");
                    step = 0;
                    stepTimer = 0;
                }
                break;

            case 2: // Initial rocket ascent
                if (mc.player.isGliding()) {
                    if (stepTimer == initialRocketDelay.get()) {
                        useFirework();
                        info("Fired initial rocket for ascent.");
                    } else if (stepTimer > initialRocketDelay.get() && stepTimer % fireworkInterval.get() == 0) {
                        useFirework();
                    }
                    targetPitchOverride = -85f;

                    if (currentHeightAboveStart >= initialAscentHeight.get()) {
                        step = 3;
                        stepTimer = 0;
                        info("Reached initial ascent height, preparing mid-air elytra swap (off).");
                    }
                } else {
                    info("Lost gliding during initial ascent, restarting from step 1 after cooldown.");
                    glideFailCooldown = System.currentTimeMillis() + 1000; // 1 second cooldown
                    step = 1;
                    stepTimer = 0;
                }
                break;

            case 3: // Mid-air elytra OFF (first part of double swap)
                // --- ADDED GLIDE CHECK ---
                if (!mc.player.isGliding()) {
                    info("Lost gliding during mid-air elytra OFF, restarting from step 1 after cooldown.");
                    glideFailCooldown = System.currentTimeMillis() + 1000; // 1 second cooldown
                    step = 1; // Go back to jump/glide initiation
                    stepTimer = 0;
                    return; // Crucial to return after changing step
                }
                // --- END ADDED GLIDE CHECK ---

                targetPitchOverride = 0f;
                
                // Only trigger ElytraSwap once when entering this step
                if (stepTimer == 0) {
                    setElytraState(false);
                }

                if (mc.player.getEquippedStack(EquipmentSlot.CHEST).getItem() != Items.ELYTRA) {
                    step = 4;
                    stepTimer = 0;
                    elytraSwapTriggered = false;
                    info("Elytra removed mid-air, waiting to re-equip.");
                } else if (stepTimer > elytraSwapTimeout.get()) {
                    info("Failed to remove elytra mid-air within timeout, restarting sequence from step 0.");
                    step = 0;
                    stepTimer = 0;
                }
                break;

            case 4: // Mid-air elytra ON (second part of double swap)
                // --- ADDED GLIDE CHECK ---
                if (!mc.player.isGliding()) {
                    info("Lost gliding during mid-air elytra ON, restarting from step 1 after cooldown.");
                    glideFailCooldown = System.currentTimeMillis() + 1000; // 1 second cooldown
                    step = 1; // Go back to jump/glide initiation
                    stepTimer = 0;
                    return; // Crucial to return after changing step
                }
                // --- END ADDED GLIDE CHECK ---

                targetPitchOverride = 0f;
                
                // Only trigger ElytraSwap once when ready
                if (stepTimer == reEquipElytraDelay.get()) {
                    setElytraState(true);
                }

                if (stepTimer >= reEquipElytraDelay.get() && 
                    mc.player.getEquippedStack(EquipmentSlot.CHEST).getItem() == Items.ELYTRA) {
                    step = 5;
                    stepTimer = 0;
                    elytraSwapTriggered = false;
                    info("Elytra re-equipped mid-air, starting dive sequence.");
                } else if (stepTimer > reEquipElytraDelay.get() + elytraSwapTimeout.get()) {
                    info("Failed to re-equip elytra mid-air within timeout, restarting sequence from step 0.");
                    step = 0;
                    stepTimer = 0;
                }
                break;

            case 5: // Diving with rockets towards target
                if (mc.player.isGliding()) {
                    targetPitchOverride = Float.NaN;

                    if (mc.player.getVelocity().y < 0.5 && stepTimer % fireworkInterval.get() == 0) {
                        useFirework();
                    }

                    if (mc.player.distanceTo(target) <= attackDistance.get()) {
                        step = 6;
                        stepTimer = 0;
                        info("Within attack distance, preparing final disengage.");
                    }
                } else {
                    info("Lost gliding during dive, restarting from step 1 after cooldown.");
                    glideFailCooldown = System.currentTimeMillis() + 1000; // 1 second cooldown
                    step = 1;
                    stepTimer = 0;
                }
                break;

            case 6: // Final attack preparation (disengage elytra for real)
                targetPitchOverride = Float.NaN;
                
                // Only trigger ElytraSwap once when entering this step
                if (stepTimer == 0) {
                    setElytraState(false);
                }

                if (mc.player.getEquippedStack(EquipmentSlot.CHEST).getItem() != Items.ELYTRA) {
                    step = 7;
                    stepTimer = 0;
                    elytraSwapTriggered = false;
                    info("Elytra removed for final attack.");
                } else if (stepTimer > elytraSwapTimeout.get()) {
                    info("Failed to remove elytra for final attack within timeout, restarting from step 0.");
                    step = 0;
                    stepTimer = 0;
                }
                break;

            case 7: // Equip mace and attack
                targetPitchOverride = Float.NaN;
                if (equipMace()) {
                    attack();
                }
                if (stepTimer > 10) {
                    step = 8;
                    stepTimer = 0;
                    info("Mace attack complete, entering recovery phase.");
                }
                break;

            case 8: // Recover from attack (wait for knockback/fall, reset)
                targetPitchOverride = Float.NaN;
                if (!mc.player.isOnGround() && mc.player.getVelocity().y < -0.1) {
                    info("Falling after attack, attempting to re-engage elytra for next cycle.");
                    step = 0;
                    stepTimer = 0;
                } else if (mc.player.isOnGround() && stepTimer > 20) {
                    info("On ground after attack, resetting sequence for next cycle.");
                    step = 0;
                    stepTimer = 0;
                }
                break;
        }
    }

    @EventHandler
    private void onRender3D(Render3DEvent event) {
        if (mc.player == null || target == null) return;

        if (!Float.isNaN(targetPitchOverride)) {
            mc.player.setPitch(targetPitchOverride);
            return;
        }

        if (step != 5) return;

        double distanceToTarget = mc.player.distanceTo(target);
        if (distanceToTarget > aimRange.get()) return;

        Vec3d playerPos = mc.player.getEyePos();
        Box hitbox = target.getBoundingBox();
        Vec3d targetPos = hitbox.getCenter();

        double x = targetPos.x - playerPos.x;
        double y = targetPos.y - playerPos.y;
        double z = targetPos.z - playerPos.z;

        double distanceXZ = Math.sqrt(x * x + z * z);

        float targetYaw = (float) Math.toDegrees(Math.atan2(z, x)) - 90.0F;
        float targetPitch = (float) Math.toDegrees(-Math.atan2(y, distanceXZ));

        float currentYaw = mc.player.getYaw();
        float currentPitch = mc.player.getPitch();

        float deltaYaw = targetYaw - currentYaw;
        if (deltaYaw > 180) deltaYaw -= 360;
        if (deltaYaw < -180) deltaYaw += 360;

        float deltaPitch = targetPitch - currentPitch;

        float newYaw;
        float newPitch;

        if (smoothAim.get()) {
            float frameAimSpeed = (float) (aimSpeed.get() * event.frameTime * 60);
            newYaw = currentYaw + deltaYaw * frameAimSpeed;
            newPitch = currentPitch + deltaPitch * frameAimSpeed;
        } else {
            newYaw = targetYaw;
            newPitch = targetPitch;
        }

        newPitch = Math.max(-90, Math.min(90, newPitch));
        
        mc.player.setYaw(newYaw);
        mc.player.setPitch(newPitch);
    }
}