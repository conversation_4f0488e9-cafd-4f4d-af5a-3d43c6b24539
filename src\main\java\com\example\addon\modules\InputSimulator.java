package com.example.addon.modules;

import net.minecraft.client.MinecraftClient;
import net.minecraft.client.option.KeyBinding;
import net.minecraft.client.util.InputUtil;

public class InputSimulator {

    public static void simulateLeftClickPress() {
        MinecraftClient client = MinecraftClient.getInstance();
        if (client.options.attackKey == null) {
            System.out.println("InputSimulator: attackKey is null!");
            return;
        }

        // Simulate pressing the attack key (left click)
        KeyBinding.onKeyPressed(client.options.attackKey.getDefaultKey());
        System.out.println("InputSimulator: Key pressed.");

        // Release the key immediately after pressing
        KeyBinding.setKeyPressed(client.options.attackKey.getDefaultKey(), false);
        System.out.println("InputSimulator: Key released.");
    }

    public static void simulateRightClickPress() {
        MinecraftClient client = MinecraftClient.getInstance();
        if (client.options.useKey == null) {
            System.out.println("InputSimulator: useKey is null!");
            return;
        }

        // Simulate pressing the use key (right click)
        KeyBinding.onKeyPressed(client.options.useKey.getDefaultKey());
        System.out.println("InputSimulator: Right click key pressed.");

        // Release the key immediately after pressing
        KeyBinding.setKeyPressed(client.options.useKey.getDefaultKey(), false);
        System.out.println("InputSimulator: Right click key released.");
    }
}