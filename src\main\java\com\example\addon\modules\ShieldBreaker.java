package com.example.addon.modules;

import com.example.addon.AddonTemplate;
import meteordevelopment.meteorclient.events.entity.player.AttackEntityEvent;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.systems.modules.Modules;
import meteordevelopment.meteorclient.utils.player.FindItemResult;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.option.KeyBinding;
import net.minecraft.entity.Entity;
import net.minecraft.entity.LivingEntity;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.item.AxeItem;
import net.minecraft.item.ItemStack;
import net.minecraft.util.Hand;

import java.lang.reflect.Method;

import static meteordevelopment.meteorclient.MeteorClient.mc;

public class ShieldBreaker extends Module {
    private final SettingGroup sgGeneral = settings.getDefaultGroup();

    private enum AttackMode {
        Packet,
        KeyPress,
        LeftClick
    }

    private final Setting<AttackMode> attackMode = sgGeneral.add(new EnumSetting.Builder<AttackMode>()
        .name("attack-mode")
        .description("The method used to attack the target.")
        .defaultValue(AttackMode.LeftClick)
        .build()
    );

    private final Setting<Integer> swapBackDelay = sgGeneral.add(new IntSetting.Builder()
        .name("swap-back-delay")
        .description("Delay in ticks before swapping back to the original weapon.")
        .defaultValue(2)
        .min(0)
        .sliderMax(20)
        .build()
    );

    private final Setting<Double> minCharge = sgGeneral.add(new DoubleSetting.Builder()
        .name("min-charge")
        .description("Minimum attack charge percentage required to break shields.")
        .defaultValue(0.8)
        .min(0.0)
        .max(1.0)
        .sliderMin(0.0)
        .sliderMax(1.0)
        .build()
    );

    private final Setting<Boolean> requireShield = sgGeneral.add(new BoolSetting.Builder()
        .name("require-shield")
        .description("Only attack players that are actively blocking with a shield.")
        .defaultValue(true)
        .build()
    );

    private int originalSlot = -1;
    private int swapBackTimer = -1;
    private boolean hasSwappedToAxe = false;

    private void performLeftClick() {
        if (mc.options.attackKey != null) {
            KeyBinding.onKeyPressed(mc.options.attackKey.getDefaultKey());
        }
    }
    private Method doAttackMethod = null;

    public ShieldBreaker() {
        super(AddonTemplate.CATEGORY, "shield-breaker", "Breaks opponent's shields by swapping to an axe and attacking.");
        initializeAttackMethod();
    }

    private void initializeAttackMethod() {
        try {
            // Try different method names - 1.21.4 may use a different obfuscated name
            try {
                doAttackMethod = mc.getClass().getDeclaredMethod("method_1536");
            } catch (NoSuchMethodException e1) {
                try {
                    // Try the actual method name (in development environment)
                    doAttackMethod = mc.getClass().getDeclaredMethod("doAttack");
                } catch (NoSuchMethodException e2) {
                    // If all attempts fail
                    error("Failed to find Minecraft's attack method via reflection. " +
                        "Module will use alternative attack method.");
                    return;
                }
            }
            
            // Make the method accessible
            doAttackMethod.setAccessible(true);
            info("Successfully found attack method via reflection for ShieldBreaker.");
        } catch (Exception e) {
            error("ShieldBreaker: Failed to initialize attack method: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    public void onDeactivate() {
        // Reset state on deactivation
        if (originalSlot != -1 && mc.player != null) {
            InvUtils.swap(originalSlot, false);
        }
        reset();
    }

    private void reset() {
        originalSlot = -1;
        swapBackTimer = -1;
        hasSwappedToAxe = false;
    }

    @EventHandler
    private void onTick(TickEvent.Pre event) {
        if (mc.player == null) return;

        // Handle swap back timer
        if (swapBackTimer > 0) {
            swapBackTimer--;
        } else if (swapBackTimer == 0) {
            // Swap back to original slot
            if (originalSlot != -1) {
                InvUtils.swap(originalSlot, false);
                originalSlot = -1;
            }
            swapBackTimer = -1;
            hasSwappedToAxe = false;
        }
    }

    @EventHandler
    private void onAttack(AttackEntityEvent event) {
        if (mc.player == null || mc.world == null) return;
        
        // Check if another module has already taken action this tick
        // Give priority to this module over Autoclicker
        if (hasAnotherModuleTakenAction() && !Modules.get().get(Autoclicker.class).isActive()) {
            info("Another module has already taken action this tick. Skipping ShieldBreaker.");
            return;
        }

        // Don't interfere with StunSlam
        if (Modules.get().get(StunSlam.class).isActive()) return;

        Entity target = event.entity;
        
        // Check if target is a living player
        if (!(target instanceof PlayerEntity)) return;
        
        PlayerEntity playerTarget = (PlayerEntity) target;
        
        // Check if target is blocking with a shield
        if (requireShield.get() && !playerTarget.isBlocking()) return;
        
        // No fall distance requirement - always proceed

        // Check if we're already handling a shield break
        if (hasSwappedToAxe) return;

        // Check if we're already holding an axe
        if (isHoldingAxe()) {
            // Check attack charge
            if (mc.player.getAttackCooldownProgress(0.0F) < minCharge.get()) {
                return;
            }
            // We're already holding an axe, just attack
            attackTarget(playerTarget);
            // Set timer to swap back
            swapBackTimer = swapBackDelay.get();
        } else {
            // Find an axe in hotbar
            FindItemResult axeResult = InvUtils.findInHotbar(itemStack -> itemStack.getItem() instanceof AxeItem);
            if (axeResult.found()) {
                // Store original slot
                FindItemResult originalSlotResult = InvUtils.findInHotbar(mc.player.getMainHandStack().getItem());
                if (originalSlotResult.found()) {
                    originalSlot = originalSlotResult.slot();
                }
                
                // Swap to axe
                InvUtils.swap(axeResult.slot(), false);
                hasSwappedToAxe = true;

                // Check attack charge after swapping to axe
                if (mc.player.getAttackCooldownProgress(0.0F) < minCharge.get()) {
                    return;
                }
                
                // Attack with axe
                attackTarget(playerTarget);
                
                // Set timer to swap back
                swapBackTimer = swapBackDelay.get();
            } else {
                // No axe found
                info("No axe found in hotbar.");
            }
        }
    }

    private void attackTarget(PlayerEntity target) {
        if (doAttackMethod == null) {
            // Re-initialize if it's null - may have been reset
            initializeAttackMethod();
            
            // If still null after re-initialization, use alternative method
            if (doAttackMethod == null) {
                error("ShieldBreaker: Attack method is null. Using alternative attack method.");
                // Fall back to direct interaction
                if (mc.interactionManager != null && target != null) {
                    mc.interactionManager.attackEntity(mc.player, target);
                    mc.player.swingHand(Hand.MAIN_HAND);
                }
                return;
            }
        }
        
        if (attackMode.get() == AttackMode.LeftClick) {
            performLeftClick();
        } else {
            try {
                doAttackMethod.invoke(mc);
            } catch (Exception e) {
                error("Failed to invoke attack method: " + e.getMessage());
                // Fall back to packet mode
                if (mc.interactionManager != null && target != null) {
                    mc.interactionManager.attackEntity(mc.player, target);
                    mc.player.swingHand(Hand.MAIN_HAND);
                }
            }
        }
        
        // Mark that this module is taking action this tick
        markActionTaken();
    }

    private boolean isHoldingAxe() {
        if (mc.player == null) return false;
        return mc.player.getMainHandStack().getItem() instanceof AxeItem;
    }

    // Add a method to check if another module has taken action this tick
    private boolean hasAnotherModuleTakenAction() {
        // Check if MaceAura has taken action
        try {
            Class<?> maceAuraClass = Class.forName("com.example.addon.modules.MaceAura");
            java.lang.reflect.Field actionTakenField = maceAuraClass.getDeclaredField("actionTakenThisTick");
            actionTakenField.setAccessible(true);
            return actionTakenField.getBoolean(null);
        } catch (Exception e) {
            // If we can't check, assume no conflict
            return false;
        }
    }

    // Mark that this module is taking action this tick
    private void markActionTaken() {
        try {
            Class<?> maceAuraClass = Class.forName("com.example.addon.modules.MaceAura");
            java.lang.reflect.Field actionTakenField = maceAuraClass.getDeclaredField("actionTakenThisTick");
            actionTakenField.setAccessible(true);
            actionTakenField.setBoolean(null, true);
        } catch (Exception e) {
            // Ignore if we can't set the flag
        }
    }
}