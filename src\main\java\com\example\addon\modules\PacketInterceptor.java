package com.example.addon.modules;

import com.example.addon.AddonTemplate;
import com.example.addon.utils.RaycastUtils;
import meteordevelopment.meteorclient.events.packets.PacketEvent;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.systems.modules.Modules;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.client.MinecraftClient;
import net.minecraft.entity.Entity;
import net.minecraft.network.packet.c2s.play.PlayerActionC2SPacket;
import net.minecraft.network.packet.c2s.play.PlayerInteractEntityC2SPacket;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Vec3d;

import static meteordevelopment.meteorclient.MeteorClient.mc;

public class PacketInterceptor extends Module {
    
    public PacketInterceptor() {
        super(AddonTemplate.CATEGORY, "packet-interceptor", "Intercepts and validates packets before sending to server.");
    }

    @EventHandler
    private void onPacketSend(PacketEvent.Send event) {
        if (mc.player == null || mc.world == null) return;

        // Intercept PlayerActionC2SPacket (block breaking/digging)
        if (event.packet instanceof PlayerActionC2SPacket packet) {
            if (shouldBlockPlayerAction(packet)) {
                event.cancel();
                if (mc.player != null) {
                    mc.player.sendMessage(net.minecraft.text.Text.literal("§7Block breaking blocked by packet interceptor"), true);
                }
            }
        }
        
        // Intercept PlayerInteractEntityC2SPacket (entity attacks)
        else if (event.packet instanceof PlayerInteractEntityC2SPacket packet) {
            if (shouldBlockEntityInteraction(packet)) {
                event.cancel();
                if (mc.player != null) {
                    mc.player.sendMessage(net.minecraft.text.Text.literal("§7Entity attack blocked by packet interceptor"), true);
                }
            }
        }
    }

    private boolean shouldBlockPlayerAction(PlayerActionC2SPacket packet) {
        Autoclicker autoclicker = Modules.get().get(Autoclicker.class);
        if (autoclicker == null || !autoclicker.shouldBlockObstructedAttacks()) {
            return false; // Don't block if setting is disabled
        }

        // Block ALL block-related actions when the setting is enabled
        PlayerActionC2SPacket.Action action = packet.getAction();
        switch (action) {
            case START_DESTROY_BLOCK:
            case ABORT_DESTROY_BLOCK:
            case STOP_DESTROY_BLOCK:
                return true; // Block all block breaking actions
            default:
                return false; // Allow other actions (like item use, etc.)
        }
    }

    private boolean shouldBlockEntityInteraction(PlayerInteractEntityC2SPacket packet) {
        Autoclicker autoclicker = Modules.get().get(Autoclicker.class);
        if (autoclicker == null || !autoclicker.shouldBlockObstructedAttacks()) {
            return false; // Don't block if setting is disabled
        }

        // Get the target entity from the packet
        Entity targetEntity = getEntityFromPacket(packet);
        if (targetEntity == null) {
            return true; // Block if we can't identify the target
        }

        // Validate the entity attack
        return !validateEntityAttack(targetEntity, autoclicker);
    }

    private Entity getEntityFromPacket(PlayerInteractEntityC2SPacket packet) {
        // Use reflection to get the entity ID from the packet (similar to DTap module approach)
        try {
            int entityId = -1;

            // This is more robust as it doesn't depend on the field name, only the type.
            for (java.lang.reflect.Field field : PlayerInteractEntityC2SPacket.class.getDeclaredFields()) {
                if (field.getType() == int.class) {
                    field.setAccessible(true);
                    entityId = field.getInt(packet);
                    break; // Assume the first and only int is the entity ID
                }
            }

            if (entityId == -1) {
                return null; // No entity ID found
            }

            // Find the entity by ID in the world
            return mc.world.getEntityById(entityId);
        } catch (Exception e) {
            // If reflection fails, return null
            return null;
        }
    }

    private boolean validateEntityAttack(Entity targetEntity, Autoclicker autoclicker) {
        if (targetEntity == null || mc.player == null) {
            return false; // Block if we can't validate
        }

        // Check range using autoclicker's attack range setting
        double distance = mc.player.distanceTo(targetEntity);
        if (distance > autoclicker.getAttackRange()) {
            return false; // Block - out of range
        }

        // Check if entity is obstructed by blocks
        if (RaycastUtils.isEntityObstructedByBlock(targetEntity)) {
            return false; // Block - obstructed by blocks
        }

        // Check if the player is actually facing the target using raycast
        if (!RaycastUtils.intersectsHitbox(targetEntity, autoclicker.getAttackRange())) {
            return false; // Block - not facing the target properly
        }

        return true; // Allow the attack
    }
}
