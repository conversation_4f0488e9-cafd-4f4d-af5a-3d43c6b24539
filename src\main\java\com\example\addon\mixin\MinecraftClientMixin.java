package com.example.addon.mixin;

import com.example.addon.modules.ActionCoordinator;
import meteordevelopment.meteorclient.systems.modules.Modules;
import net.minecraft.client.MinecraftClient;
import net.minecraft.entity.Entity;
import net.minecraft.util.hit.BlockHitResult;
import net.minecraft.util.hit.EntityHitResult;
import net.minecraft.util.hit.HitResult;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;

@Mixin(MinecraftClient.class)
public class MinecraftClientMixin {
    
    @Inject(method = "doAttack", at = @At("HEAD"), cancellable = true)
    private void onDoAttack(CallbackInfoReturnable<Boolean> cir) {
        MinecraftClient mc = (MinecraftClient) (Object) this;

        // Check what we're targeting
        HitResult hitResult = mc.crosshairTarget;
        if (hitResult == null) return;

        // Use ActionCoordinator for centralized validation
        if (hitResult.getType() == HitResult.Type.ENTITY) {
            Entity targetEntity = ((EntityHitResult) hitResult).getEntity();
            if (!ActionCoordinator.validateAction(ActionCoordinator.ActionType.ATTACK_ENTITY, targetEntity, null)) {
                cir.setReturnValue(false);
                if (mc.player != null) {
                    mc.player.sendMessage(net.minecraft.text.Text.literal("§7Entity attack blocked - out of range or obstructed"), true);
                }
            }
        } else if (hitResult.getType() == HitResult.Type.BLOCK) {
            BlockHitResult blockHit = (BlockHitResult) hitResult;
            if (!ActionCoordinator.validateAction(ActionCoordinator.ActionType.ATTACK_BLOCK, null, blockHit.getBlockPos())) {
                cir.setReturnValue(false);
                if (mc.player != null) {
                    mc.player.sendMessage(net.minecraft.text.Text.literal("§7All block breaking blocked"), true);
                }
            }
        }
    }


}