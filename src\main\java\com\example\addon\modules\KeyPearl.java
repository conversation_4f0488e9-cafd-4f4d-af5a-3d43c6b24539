package com.example.addon.modules;

import com.example.addon.AddonTemplate;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.settings.DoubleSetting;
import meteordevelopment.meteorclient.settings.Setting;
import meteordevelopment.meteorclient.settings.SettingGroup;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.utils.player.FindItemResult;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.item.Items;
import net.minecraft.util.Hand;

import static meteordevelopment.meteorclient.MeteorClient.mc;

public class KeyPearl extends Module {
    private final SettingGroup sgGeneral = settings.getDefaultGroup();

    private final Setting<Double> swapDelay = sgGeneral.add(new DoubleSetting.Builder()
        .name("swap-delay")
        .description("Delay in ticks after swapping to the Ender Pearl.")
        .defaultValue(1.0)
        .min(0.0)
        .max(10.0)
        .sliderMin(0.0)
        .sliderMax(10.0)
        .build()
    );

    private final Setting<Double> throwDelay = sgGeneral.add(new DoubleSetting.Builder()
        .name("throw-delay")
        .description("Delay in ticks after throwing the Ender Pearl before swapping back.")
        .defaultValue(3.0)
        .min(0.0)
        .max(20.0)
        .sliderMin(0.0)
        .sliderMax(20.0)
        .build()
    );

    private final Setting<Double> swapBackDelay = sgGeneral.add(new DoubleSetting.Builder()
        .name("swap-back-delay")
        .description("Delay in ticks before swapping back to original slot.")
        .defaultValue(1.0)
        .min(0.0)
        .max(10.0)
        .sliderMin(0.0)
        .sliderMax(10.0)
        .build()
    );

    public KeyPearl() {
        super(AddonTemplate.CATEGORY, "auto-throw-pearl", "Swaps to an Ender Pearl, throws it, and swaps back automatically.");
    }

    private int timer = -1;
    private int state = 0; // 0: inactive, 1: swap to pearl, 2: throw pearl, 3: swap back
    private int originalHotbarSlot = -1;
    private FindItemResult pearlItem;

    @Override
    public void onActivate() {
        if (mc.player == null || mc.world == null) {
            error("Player or world is null, cannot activate.");
            toggle();
            return;
        }

        // Find ender pearl in hotbar
        pearlItem = InvUtils.findInHotbar(Items.ENDER_PEARL);

        if (!pearlItem.found()) {
            error("No Ender Pearls found in hotbar.");
            toggle();
            return;
        }

        // Store original slot using the public getter
        originalHotbarSlot = mc.player.getInventory().getSelectedSlot();

        // Start sequence
        state = 1; // Start by swapping to pearl
        timer = swapDelay.get().intValue();
        info("Initiating auto pearl throw sequence.");
    }

    @Override
    public void onDeactivate() {
        resetState();
    }

    @EventHandler
    private void onTick(TickEvent.Pre event) {
        if (mc.player == null || mc.world == null) {
            resetState();
            toggle(); // Deactivate if player/world becomes null mid-sequence
            return;
        }

        if (timer > 0) {
            timer--;
            return;
        }

        switch (state) {
            case 1: // Swap to ender pearl
                InvUtils.swap(pearlItem.slot(), false);
                state = 2;
                timer = throwDelay.get().intValue();
                break;
            case 2: // Throw ender pearl
                mc.interactionManager.interactItem(mc.player, Hand.MAIN_HAND);
                state = 3;
                timer = swapBackDelay.get().intValue();
                break;
            case 3: // Swap back to original slot
                if (originalHotbarSlot != -1) {
                    InvUtils.swap(originalHotbarSlot, false);
                }
                info("Ender Pearl thrown and swapped back.");
                toggle(); // Sequence complete, deactivate module
                break;
        }
    }

    private void resetState() {
        timer = -1;
        state = 0;
        originalHotbarSlot = -1;
        pearlItem = null;
    }
}