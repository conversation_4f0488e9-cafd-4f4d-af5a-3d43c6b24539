package com.example.addon.test;

import com.example.addon.utils.RandomizedAimingUtils;
import net.minecraft.util.math.Vec3d;

/**
 * Simple test class to verify randomized aiming functionality.
 * This can be used to manually test the randomization algorithms.
 */
public class RandomizedAimingTest {
    
    /**
     * Test the basic randomization functionality.
     */
    public static void testBasicRandomization() {
        
        
        // Test rotation randomization
        float[] originalRotations = {45.0f, -10.0f}; // yaw, pitch
        
        
        
        // Test with different randomization amounts
        double[] randomizationAmounts = {0.0, 0.5, 1.0, 2.0};
        
        for (double amount : randomizationAmounts) {
            float[] randomizedRotations = RandomizedAimingUtils.addRandomizationToRotation(
                originalRotations[0], originalRotations[1], amount
            );
            
            float yawDiff = Math.abs(randomizedRotations[0] - originalRotations[0]);
            float pitchDiff = Math.abs(randomizedRotations[1] - originalRotations[1]);
            
            
           
             

            
        }
        
        
    }
    
    /**
     * Test the circular random offset functionality.
     */
    public static void testCircularRandomOffset() {
        
        
        double[] radii = {0.5, 1.0, 2.0};
        
        for (double radius : radii) {
            
            
            // Generate multiple offsets to show distribution
            for (int i = 0; i < 5; i++) {
                Vec3d offset = RandomizedAimingUtils.getCircularRandomOffset(radius);
                double actualRadius = Math.sqrt(offset.x * offset.x + offset.y * offset.y);
                
                
            }
            System.out.println();
        }
        
        
    }
    
    /**
     * Test randomization consistency (same input should produce different outputs).
     */
    public static void testRandomizationConsistency() {
        
        
        float originalYaw = 90.0f;
        float originalPitch = 0.0f;
        double randomizationAmount = 1.0;
        
        
        
        
        
        boolean allSame = true;
        float[] firstResult = null;
        
        for (int i = 0; i < 10; i++) {
            float[] result = RandomizedAimingUtils.addRandomizationToRotation(
                originalYaw, originalPitch, randomizationAmount
            );
            
            if (firstResult == null) {
                firstResult = result.clone();
            } else {
                if (Math.abs(result[0] - firstResult[0]) > 0.001f || 
                    Math.abs(result[1] - firstResult[1]) > 0.001f) {
                    allSame = false;
                }
            }
            
             
                             
        }
        
        
        
        
    }
    
    /**
     * Run all tests.
     */
    public static void runAllTests() {
        testBasicRandomization();
        System.out.println();
        testCircularRandomOffset();
        System.out.println();
        testRandomizationConsistency();
    }
}
