package com.example.addon.modules;

import com.example.addon.utils.SmoothAimingUtils;
import meteordevelopment.meteorclient.events.render.Render3DEvent;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Categories;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.utils.player.FindItemResult;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import meteordevelopment.meteorclient.utils.player.Rotations;
import com.example.addon.utils.KeyPressSimulator;
import meteordevelopment.meteorclient.utils.world.BlockUtils;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.block.Blocks;
import net.minecraft.entity.player.PlayerEntity;
import org.lwjgl.glfw.GLFW;
import net.minecraft.item.Items;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Direction;
import net.minecraft.util.math.Vec3d;
import net.minecraft.block.BlockState;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class AutoWeb extends Module {
    private final SettingGroup sgGeneral = settings.getDefaultGroup();
    private final SettingGroup sgAiming = settings.createGroup("Aiming");
    private final SettingGroup sgCombat = settings.createGroup("Combat");

    // General settings
    private final Setting<Double> range = sgGeneral.add(new DoubleSetting.Builder()
        .name("range")
        .description("Maximum range to place webs.")
        .defaultValue(4.0)
        .min(0.0)
        .max(6.0)
        .sliderMax(6.0)
        .build()
    );

    private final Setting<Boolean> selfProtect = sgGeneral.add(new BoolSetting.Builder()
        .name("self-protect")
        .description("Avoid placing webs on yourself.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> predictMovement = sgGeneral.add(new BoolSetting.Builder()
        .name("predict-movement")
        .description("Predict player movement and place webs ahead.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Integer> delay = sgGeneral.add(new IntSetting.Builder()
        .name("delay")
        .description("Delay between web placements in ticks.")
        .defaultValue(3)
        .min(0)
        .max(20)
        .sliderMax(20)
        .build()
    );

    private final Setting<Boolean> onlyWhileFalling = sgGeneral.add(new BoolSetting.Builder()
        .name("only-while-falling")
        .description("Only place webs when the target player is airborne (falling or jumping).")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> targetInAirOnly = sgGeneral.add(new BoolSetting.Builder()
        .name("target-in-air-only")
        .description("Only place webs when the target is in the air (not on ground).")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> sophisticatedAlgorithm = sgGeneral.add(new BoolSetting.Builder()
        .name("sophisticated-algorithm")
        .description("Use advanced placement algorithm with physics prediction and scoring.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Double> predictionConfidence = sgGeneral.add(new DoubleSetting.Builder()
        .name("prediction-confidence")
        .description("Minimum confidence required for prediction-based placement.")
        .defaultValue(0.3)
        .min(0.0)
        .max(1.0)
        .sliderMax(1.0)
        .visible(() -> sophisticatedAlgorithm.get())
        .build()
    );

    private final Setting<Boolean> prioritizeHeadCapture = sgGeneral.add(new BoolSetting.Builder()
        .name("prioritize-head-capture")
        .description("Give higher priority to capturing the target's head/upper body.")
        .defaultValue(true)
        .visible(() -> sophisticatedAlgorithm.get())
        .build()
    );

    private final Setting<Boolean> debugMode = sgGeneral.add(new BoolSetting.Builder()
        .name("debug-mode")
        .description("Print debug information about placement decisions.")
        .defaultValue(false)
        .visible(() -> sophisticatedAlgorithm.get())
        .build()
    );

    // Combat settings
    private final Setting<Boolean> inCombatCheck = sgCombat.add(new BoolSetting.Builder()
        .name("in-combat-check")
        .description("Only activate when in combat.")
        .defaultValue(false)
        .build()
    );

    private final Setting<Double> combatRange = sgCombat.add(new DoubleSetting.Builder()
        .name("combat-range")
        .description("Range to consider being in combat.")
        .defaultValue(6.0)
        .min(0.0)
        .max(10.0)
        .sliderMax(10.0)
        .visible(() -> inCombatCheck.get())
        .build()
    );

    private final Setting<Integer> combatTimeout = sgCombat.add(new IntSetting.Builder()
        .name("combat-timeout")
        .description("Ticks to remain in combat after last enemy contact.")
        .defaultValue(100)
        .min(0)
        .max(400)
        .sliderMax(400)
        .visible(() -> inCombatCheck.get())
        .build()
    );

    // Aiming settings
    private final Setting<Boolean> smoothAiming = sgAiming.add(new BoolSetting.Builder()
        .name("smooth-aiming")
        .description("Use smooth aiming when placing webs.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Double> aimSpeed = sgAiming.add(new DoubleSetting.Builder()
        .name("aim-speed")
        .description("Speed of smooth aiming.")
        .defaultValue(0.3)
        .min(0.1)
        .max(1.0)
        .sliderMax(1.0)
        .visible(() -> smoothAiming.get())
        .build()
    );

    private final Setting<Boolean> exponentialSmoothing = sgAiming.add(new BoolSetting.Builder()
        .name("exponential-smoothing")
        .description("Use exponential smoothing for even smoother aiming.")
        .defaultValue(false)
        .visible(() -> smoothAiming.get())
        .build()
    );

    private final Setting<Double> smoothingFactor = sgAiming.add(new DoubleSetting.Builder()
        .name("smoothing-factor")
        .description("Exponential smoothing factor (higher = smoother).")
        .defaultValue(0.7)
        .min(0.0)
        .max(1.0)
        .sliderMax(1.0)
        .visible(() -> smoothAiming.get() && exponentialSmoothing.get())
        .build()
    );

    private final Setting<Boolean> aimAtCenter = sgAiming.add(new BoolSetting.Builder()
        .name("aim-at-center")
        .description("Aim at the center of the block when placing.")
        .defaultValue(true)
        .visible(() -> smoothAiming.get())
        .build()
    );

    // Randomized aiming settings
    private final Setting<Boolean> enableRandomizedAiming = sgAiming.add(new BoolSetting.Builder()
        .name("randomized-aiming")
        .description("Enable randomized aiming to make block placement appear more natural.")
        .defaultValue(true)
        .visible(() -> smoothAiming.get())
        .build()
    );

    private final Setting<Double> randomizationStrength = sgAiming.add(new DoubleSetting.Builder()
        .name("randomization-strength")
        .description("How much randomization to apply to aiming (0.0 = no randomization, 1.0 = full randomization).")
        .defaultValue(0.15)
        .min(0.0)
        .max(1.0)
        .sliderMax(1.0)
        .visible(() -> smoothAiming.get() && enableRandomizedAiming.get())
        .build()
    );

    private final Setting<Boolean> useKeyPress = sgGeneral.add(new BoolSetting.Builder()
        .name("use-key-press")
        .description("Simulate key presses for placing blocks.")
        .defaultValue(false)
        .build()
    );

    private final Setting<Integer> placeKey = sgGeneral.add(new IntSetting.Builder()
        .name("place-key")
        .description("The key to simulate for placing blocks (Minecraft key code). Default is Mouse Button 2 (right click).")
        .defaultValue(GLFW.GLFW_MOUSE_BUTTON_2)
        .min(0)
        .build()
    );

    private int timer = 0;
    private long lastFrameTime = System.nanoTime();
    private BlockPos targetBlock = null;
    private boolean isAiming = false;
    private int lastCombatTime = 0;
    private PlayerEntity target = null;
    private int originalSlot = -1; // Track original item slot for swapping back

    // Track which players already have webs placed for them (no time expiration)
    private final Set<PlayerEntity> webbedPlayers = new HashSet<>();

    public AutoWeb() {
        super(Categories.Combat, "auto-web", "Automatically places webs on nearby players with smart targeting.");
    }

    @EventHandler
    private void onTick(TickEvent.Pre event) {
        // Check if another module has already taken action this tick
        if (hasAnotherModuleTakenAction()) {
            return;
        }

        // Update combat status
        updateCombatStatus();
        
        // Clean up webbed players set (remove players that are no longer webbed)
        cleanupWebbedPlayers();

        // Check combat requirement
        if (inCombatCheck.get() && !isInCombat()) {
            isAiming = false;
            targetBlock = null;
            return;
        }

        // Calculate delta time for smooth aiming
        long currentTime = System.nanoTime();
        float deltaTime = (currentTime - lastFrameTime) / 1_000_000_000.0f;
        lastFrameTime = currentTime;

        // Check if we have webs in inventory
        FindItemResult webs = InvUtils.find(Items.COBWEB);
        if (!webs.found()) {
            isAiming = false;
            targetBlock = null;
            return;
        }

        // Decrement timer
        if (timer > 0) {
            timer--;
        }

        // If we're currently aiming at a block, continue the smooth aiming process
        if (isAiming && targetBlock != null) {
            // Try to place when timer is ready
            if (timer == 0 && canPlaceAt(targetBlock) && !willAffectPlayer(targetBlock)) {
                // Store original slot before swapping
                if (originalSlot == -1) {
                    FindItemResult originalSlotResult = InvUtils.findInHotbar(mc.player.getMainHandStack().getItem());
                    originalSlot = originalSlotResult.found() ? originalSlotResult.slot() : -1;
                }

                if (webs.getHand() == null && !InvUtils.swap(webs.slot(), false)) {
                    isAiming = false;
                    targetBlock = null;
                    swapBackToOriginal();
                    return;
                }

                if (useKeyPress.get()) {
                    // Simulate key press
                    KeyPressSimulator.pressKey(placeKey.get());
                }

                if (BlockUtils.place(targetBlock, webs, false, 0)) {
                    if (useKeyPress.get()) {
                        // Simulate key release
                        KeyPressSimulator.releaseKey(placeKey.get());
                    }
                    markActionTaken(); // Notify action coordinator
                    timer = delay.get();
                    isAiming = false;
                    targetBlock = null;
                    swapBackToOriginal();
                } else {
                    swapBackToOriginal();
                }
            }
            return;
        }

        // Find target player if we're not currently aiming
            if (timer == 0) {
                target = findTarget();
                if (target != null) {
                    BlockPos placePos = getPlacementPos(target);
                    if (placePos != null && canPlaceAt(placePos) && hasReachableAdjacentBlock(placePos) && !willAffectPlayer(placePos)) {
                        targetBlock = placePos;
                        isAiming = smoothAiming.get();

                        // Mark this player as webbed (no expiration)
                        webbedPlayers.add(target);

                        if (!smoothAiming.get()) {
                            // Store original slot before swapping
                            if (originalSlot == -1) {
 FindItemResult originalSlotResult = InvUtils.findInHotbar(mc.player.getMainHandStack().getItem());
 originalSlot = originalSlotResult.found() ? originalSlotResult.slot() : -1;
                            }

                            // Instant placement without smooth aiming
                            if (webs.getHand() == null && !InvUtils.swap(webs.slot(), false)) {
                                swapBackToOriginal();
                                return;
                            }

                            if (useKeyPress.get()) {
                                // Simulate key press
                                KeyPressSimulator.pressKey(placeKey.get());
                            }

                            if (BlockUtils.place(placePos, webs, false, 0)) {
                                if (useKeyPress.get()) {
                                    // Simulate key release
                                    KeyPressSimulator.releaseKey(placeKey.get());
                                }
                                markActionTaken(); // Notify action coordinator
                                timer = delay.get();
                            }
                            targetBlock = null;
                            swapBackToOriginal();
                        }
                    }
                }
            }
    }

    private void updateCombatStatus() {
        if (!inCombatCheck.get()) return;

        boolean foundEnemy = false;
        for (PlayerEntity player : mc.world.getPlayers()) {
            if (player == mc.player) continue;
            if (player.isDead() || player.getHealth() <= 0) continue;
            
            double distance = mc.player.distanceTo(player);
            if (distance <= combatRange.get()) {
                foundEnemy = true;
                break;
            }
        }

        if (foundEnemy) {
            lastCombatTime = combatTimeout.get();
        } else if (lastCombatTime > 0) {
            lastCombatTime--;
        }
    }

    private boolean isInCombat() {
        return lastCombatTime > 0;
    }

    private void cleanupWebbedPlayers() {
        webbedPlayers.removeIf(player -> {
            // Remove if player is dead, too far away, or no longer in web
            if (player.isDead() || mc.player.distanceTo(player) > range.get() * 2) {
                return true;
            }
            
            // Check if player is still in web
            return !isPlayerInWeb(player);
        });
    }

    private PlayerEntity findTarget() {
        PlayerEntity target = null;
        double closestDistance = Double.MAX_VALUE;

        for (PlayerEntity player : mc.world.getPlayers()) {
            if (player == mc.player) continue;
            if (player.isDead() || player.getHealth() <= 0) continue;
            if (selfProtect.get() && player == mc.player) continue;

            double distance = mc.player.distanceTo(player);
            if (distance > range.get()) continue;

            // Check if target is airborne (falling/jumping) when setting is enabled
            if (targetInAirOnly.get() && !isPlayerAirborne(player)) continue;

            // Skip if this player already has a web placed for them
            if (webbedPlayers.contains(player)) continue;

            // Check if player is already in a web
            if (isPlayerInWeb(player)) continue;

            if (distance < closestDistance) {
                closestDistance = distance;
                target = player;
            }
        }

        return target;
    }

    private boolean isPlayerAirborne(PlayerEntity player) {
        // Consider player airborne if they are not on the ground
        return !player.isOnGround();
    }

    private boolean isPlayerInWeb(PlayerEntity player) {
        BlockPos playerPos = new BlockPos((int) Math.floor(player.getX()), 
                                        (int) Math.floor(player.getY()), 
                                        (int) Math.floor(player.getZ()));
        
        // Check current position and position above (head level)
        return mc.world.getBlockState(playerPos).getBlock() == Blocks.COBWEB ||
               mc.world.getBlockState(playerPos.up()).getBlock() == Blocks.COBWEB;
    }

    @EventHandler
    private void onRender3d(Render3DEvent event) {
        if (mc.player == null || target == null || targetBlock == null || !smoothAiming.get()) {
            return;
        }

        Vec3d playerPos = mc.player.getEyePos();
        Vec3d targetAimPos;

        if (aimAtCenter.get()) {
            targetAimPos = new Vec3d(targetBlock.getX() + 0.5, targetBlock.getY() + 0.5, targetBlock.getZ() + 0.5);
        } else {
            // Calculate the point on the closest face of the targetBlock
            Vec3d blockCenter = new Vec3d(targetBlock.getX() + 0.5, targetBlock.getY() + 0.5, targetBlock.getZ() + 0.5);
            Vec3d directionToBlock = blockCenter.subtract(playerPos).normalize();

            Direction closestFace = Direction.getFacing(directionToBlock.x, directionToBlock.y, directionToBlock.z);

            double x = targetBlock.getX() + 0.5;
            double y = targetBlock.getY() + 0.5;
            double z = targetBlock.getZ() + 0.5;

            switch (closestFace) {
                case DOWN: y = targetBlock.getY(); break;
                case UP: y = targetBlock.getY() + 1.0; break;
                case NORTH: z = targetBlock.getZ(); break;
                case SOUTH: z = targetBlock.getZ() + 1.0; break;
                case WEST: x = targetBlock.getX(); break;
                case EAST: x = targetBlock.getX() + 1.0; break;
            }
            targetAimPos = new Vec3d(x, y, z);
        }

        float[] rotations;

        if (enableRandomizedAiming.get()) {
            // Add subtle randomization to block placement aiming
            if (exponentialSmoothing.get()) {
                rotations = SmoothAimingUtils.calculateExponentialSmoothRotations(
                    mc.player.getYaw(),
                    mc.player.getPitch(),
                    targetAimPos,
                    playerPos,
                    aimSpeed.get() * 25.0,
                    (float) event.frameTime,
                    smoothingFactor.get()
                );
            } else {
                rotations = SmoothAimingUtils.calculateSmoothRotations(
                    mc.player.getYaw(),
                    mc.player.getPitch(),
                    targetAimPos,
                    playerPos,
                    aimSpeed.get() * 25.0,
                    (float) event.frameTime,
                    smoothingFactor.get()
                );
            }

            // Apply subtle randomization to the calculated rotations
            rotations = SmoothAimingUtils.addRandomizationToRotations(rotations, randomizationStrength.get() * 2.0);
        } else {
            // Use traditional aiming without randomization
            if (exponentialSmoothing.get()) {
                rotations = SmoothAimingUtils.calculateExponentialSmoothRotations(
                    mc.player.getYaw(),
                    mc.player.getPitch(),
                    targetAimPos,
                    playerPos,
                    aimSpeed.get() * 25.0,
                    (float) event.frameTime,
                    smoothingFactor.get()
                );
            } else {
                rotations = SmoothAimingUtils.calculateSmoothRotations(
                    mc.player.getYaw(),
                    mc.player.getPitch(),
                    targetAimPos,
                    playerPos,
                    aimSpeed.get() * 25.0,
                    (float) event.frameTime,
                    smoothingFactor.get()
                );
            }
        }

        mc.player.setYaw(rotations[0]);
        mc.player.setPitch(rotations[1]);
    }



    private BlockPos getPlacementPos(PlayerEntity target) {
        if (sophisticatedAlgorithm.get()) {
            BlockPos sophisticatedPos = getSophisticatedPlacementPos(target);
            if (sophisticatedPos != null) {
                return sophisticatedPos;
            }
        }

        // Fallback to original algorithm
        return getOriginalPlacementPos(target);
    }

    /**
     * Advanced placement algorithm that optimizes for:
     * 1. Hitting the enemy effectively
     * 2. Catching them in the web with high probability
     * 3. Using minimal webs
     * 4. Prioritizing head/upper body capture
     */
    private BlockPos getSophisticatedPlacementPos(PlayerEntity target) {
        // Advanced movement prediction considering physics
        PredictionResult prediction = predictTargetMovement(target);

        if (debugMode.get()) {
            

        }

        // Generate candidate positions with scoring
        List<PlacementCandidate> candidates = generatePlacementCandidates(target, prediction);

        // Score and rank candidates
        candidates.sort((a, b) -> Double.compare(b.score, a.score));

        if (debugMode.get()) {
            
            for (int i = 0; i < Math.min(3, candidates.size()); i++) {
                PlacementCandidate c = candidates.get(i);
                
            }
        }

        // Return the best candidate that can actually be placed
        for (PlacementCandidate candidate : candidates) {
            if (canPlaceAt(candidate.pos) && hasReachableAdjacentBlock(candidate.pos)) {
                if (debugMode.get()) {
                    
                }
                return candidate.pos;
            }
        }

        if (debugMode.get()) {
            
        }

        return null;
    }

    private static class PredictionResult {
        final Vec3d predictedPos;
        final Vec3d predictedVelocity;
        final boolean willLand;
        final int ticksToLand;
        final double confidence;

        PredictionResult(Vec3d pos, Vec3d velocity, boolean willLand, int ticksToLand, double confidence) {
            this.predictedPos = pos;
            this.predictedVelocity = velocity;
            this.willLand = willLand;
            this.ticksToLand = ticksToLand;
            this.confidence = confidence;
        }
    }

    private static class PlacementCandidate {
        final BlockPos pos;
        final double score;
        final String reason;

        PlacementCandidate(BlockPos pos, double score, String reason) {
            this.pos = pos;
            this.score = score;
            this.reason = reason;
        }
    }

    private PredictionResult predictTargetMovement(PlayerEntity target) {
        Vec3d currentPos = target.getPos();
        Vec3d velocity = target.getVelocity();

        // If target is already on ground and not moving much, they're already "landed"
        if (target.isOnGround() && velocity.length() < 0.1) {
            return new PredictionResult(currentPos, velocity, true, 0, 0.9);
        }

        // Physics-based prediction focusing on ACTUAL landing detection
        double gravity = 0.08; // Minecraft gravity per tick
        double drag = 0.98; // Minecraft drag factor

        Vec3d predictedPos = currentPos;
        Vec3d predictedVelocity = velocity;
        boolean willLand = false;
        int ticksToLand = 0;
        Vec3d landingPos = currentPos;

        // Only predict if target is actually moving or falling
        if (velocity.length() > 0.01 || !target.isOnGround()) {
            // Simulate up to 60 ticks ahead for thorough landing detection
            for (int tick = 1; tick <= 60; tick++) {
                // Apply gravity if target is airborne
                if (predictedVelocity.y > -3.92) { // Terminal velocity check
                    predictedVelocity = predictedVelocity.add(0, -gravity, 0);
                }

                // Apply drag (air resistance)
                predictedVelocity = predictedVelocity.multiply(drag);

                // Update position
                Vec3d nextPos = predictedPos.add(predictedVelocity);

                // Check if target will hit ground at this position
                if (willHitGround(nextPos) && predictedVelocity.y <= 0) {
                    willLand = true;
                    ticksToLand = tick;
                    // Adjust landing position to be ON the ground, not inside it
                    landingPos = new Vec3d(nextPos.x, Math.floor(nextPos.y) + 1, nextPos.z);
                    break;
                }

                predictedPos = nextPos;

                // Stop if velocity becomes negligible
                if (predictedVelocity.length() < 0.001) {
                    break;
                }
            }
        }

        // Use landing position if found, otherwise current position
        Vec3d finalPos = willLand ? landingPos : currentPos;

        // Calculate confidence based on landing prediction
        double confidence;
        if (willLand && ticksToLand <= 10) {
            confidence = 0.95; // Very high confidence for immediate landings
        } else if (willLand && ticksToLand <= 30) {
            confidence = 0.8; // High confidence for near-term landings
        } else if (willLand) {
            confidence = 0.6; // Medium confidence for far landings
        } else {
            confidence = 0.2; // Low confidence if no landing detected
        }

        return new PredictionResult(finalPos, predictedVelocity, willLand, ticksToLand, confidence);
    }

    private boolean willHitGround(Vec3d pos) {
        // Check the block directly below the predicted position
        BlockPos groundPos = new BlockPos((int) Math.floor(pos.x), (int) Math.floor(pos.y - 0.1), (int) Math.floor(pos.z));

        // Check if there's a solid block below
        if (!mc.world.getBlockState(groundPos).isAir()) {
            return true;
        }

        // Also check a bit further down in case of precision issues
        BlockPos lowerPos = groundPos.down();
        return !mc.world.getBlockState(lowerPos).isAir();
    }

    private List<PlacementCandidate> generatePlacementCandidates(PlayerEntity target, PredictionResult prediction) {
        List<PlacementCandidate> candidates = new ArrayList<>();

        Vec3d currentPos = target.getPos();
        Vec3d landingPos = prediction.predictedPos;

        // If target will land, ONLY focus on landing positions
        if (prediction.willLand && prediction.confidence > predictionConfidence.get()) {
            if (debugMode.get()) {
                
            }

            // Landing position candidates
            BlockPos landingFeet = new BlockPos((int) Math.floor(landingPos.x), (int) Math.floor(landingPos.y), (int) Math.floor(landingPos.z));
            BlockPos landingHead = landingFeet.up();

            // Scoring factors for landing positions
            double headBonus = prioritizeHeadCapture.get() ? 5.0 : 3.0;
            double landingBonus = 10.0; // MASSIVE bonus for actual landing positions
            double confidenceBonus = prediction.confidence * 5.0;

            // Landing head position (ABSOLUTE HIGHEST PRIORITY)
            double landingHeadScore = 200.0 + headBonus + landingBonus + confidenceBonus;
            candidates.add(new PlacementCandidate(landingHead, landingHeadScore,
                "LANDING HEAD (ticks: " + prediction.ticksToLand + ")"));

            // Landing feet position (VERY HIGH PRIORITY)
            double landingFeetScore = 180.0 + landingBonus + confidenceBonus;
            candidates.add(new PlacementCandidate(landingFeet, landingFeetScore,
                "LANDING FEET (ticks: " + prediction.ticksToLand + ")"));

            // Add positions around landing spot for comprehensive coverage
            addLandingAreaPositions(candidates, landingFeet, landingHead, landingBonus, confidenceBonus);

            if (debugMode.get()) {
                
            }
        } else {
            // Fallback to current position if no reliable landing prediction
            if (debugMode.get()) {
                
            }

            BlockPos currentFeet = new BlockPos((int) Math.floor(currentPos.x), (int) Math.floor(currentPos.y), (int) Math.floor(currentPos.z));
            BlockPos currentHead = currentFeet.up();

            double headBonus = prioritizeHeadCapture.get() ? 3.0 : 1.0;

            // Current head position
            candidates.add(new PlacementCandidate(currentHead, 100.0 + headBonus, "Current head position"));

            // Current feet position
            candidates.add(new PlacementCandidate(currentFeet, 80.0, "Current feet position"));

            // Add some strategic positions around current location
            addStrategicPositions(candidates, currentFeet, currentHead, "current");
        }

        return candidates;
    }

    private void addLandingAreaPositions(List<PlacementCandidate> candidates, BlockPos landingFeet, BlockPos landingHead, double landingBonus, double predictionBonus) {
        // Add positions around the landing spot for better coverage
        BlockPos[] landingAreaPositions = {
            // Immediate landing area (highest priority)
            landingHead.add(1, 0, 0),   // East of landing head
            landingHead.add(-1, 0, 0),  // West of landing head
            landingHead.add(0, 0, 1),   // South of landing head
            landingHead.add(0, 0, -1),  // North of landing head

            // Feet level around landing (medium priority)
            landingFeet.add(1, 0, 0),   // East of landing feet
            landingFeet.add(-1, 0, 0),  // West of landing feet
            landingFeet.add(0, 0, 1),   // South of landing feet
            landingFeet.add(0, 0, -1),  // North of landing feet
        };

        for (int i = 0; i < landingAreaPositions.length; i++) {
            BlockPos pos = landingAreaPositions[i];
            double score = (i < 4) ?
                110.0 + landingBonus + (predictionBonus * 0.8) : // Head-level around landing
                90.0 + landingBonus + (predictionBonus * 0.6);   // Feet-level around landing
            candidates.add(new PlacementCandidate(pos, score, "Landing area position " + i));
        }
    }

    private void addStrategicPositions(List<PlacementCandidate> candidates, BlockPos feetPos, BlockPos headPos, String prefix) {
        // Add positions around the target to create a web trap (for stationary targets)
        BlockPos[] strategicPositions = {
            feetPos.add(1, 0, 0),   // East
            feetPos.add(-1, 0, 0),  // West
            feetPos.add(0, 0, 1),   // South
            feetPos.add(0, 0, -1),  // North
            headPos.add(1, 0, 0),   // East head
            headPos.add(-1, 0, 0),  // West head
            headPos.add(0, 0, 1),   // South head
            headPos.add(0, 0, -1)   // North head
        };

        for (int i = 0; i < strategicPositions.length; i++) {
            BlockPos pos = strategicPositions[i];
            double score = (i < 4) ? 50.0 : 60.0; // Lower scores since these are fallback positions
            candidates.add(new PlacementCandidate(pos, score, prefix + " position " + i));
        }
    }

    /**
     * Original placement algorithm as fallback
     */
    private BlockPos getOriginalPlacementPos(PlayerEntity target) {
        Vec3d targetPos = target.getPos();
        BlockPos playerFeetPos = new BlockPos((int) Math.floor(targetPos.x),
                                            (int) Math.floor(targetPos.y),
                                            (int) Math.floor(targetPos.z));
        BlockPos playerHeadPos = playerFeetPos.up();

        // Priority order: head first, then other positions
        BlockPos[] positions;

        if (predictMovement.get()) {
            Vec3d velocity = target.getVelocity();
            if (velocity.lengthSquared() > 0.01) {
                Vec3d predictedPos = targetPos;
                // Simple prediction: add velocity over a few ticks
                for (int i = 0; i < 5; i++) { // Predict 5 ticks ahead
                    predictedPos = predictedPos.add(velocity);
                    // Simulate gravity for falling targets
                    if (!target.isOnGround()) {
                        velocity = velocity.add(0, -0.08, 0); // Approximate gravity
                    }
                }

                BlockPos predictedFeet = new BlockPos((int) Math.floor(predictedPos.x),
                                                    (int) Math.floor(predictedPos.y),
                                                    (int) Math.floor(predictedPos.z));
                BlockPos predictedHead = predictedFeet.up();

                positions = new BlockPos[] {
                    predictedHead,      // Predicted head (highest priority)
                    playerHeadPos,      // Current head
                    predictedFeet,      // Predicted feet
                    playerFeetPos,      // Current feet
                    playerFeetPos.add(1, 0, 0),  // East
                    playerFeetPos.add(-1, 0, 0), // West
                    playerFeetPos.add(0, 0, 1),  // South
                    playerFeetPos.add(0, 0, -1)  // North
                };
            } else {
                positions = new BlockPos[] {
                    playerHeadPos,      // Head (highest priority)
                    playerFeetPos,      // Feet
                    playerFeetPos.add(1, 0, 0),  // East
                    playerFeetPos.add(-1, 0, 0), // West
                    playerFeetPos.add(0, 0, 1),  // South
                    playerFeetPos.add(0, 0, -1)  // North
                };
            }
        } else {
            positions = new BlockPos[] {
                playerHeadPos,      // Head (highest priority)
                playerFeetPos,      // Feet
                playerFeetPos.add(1, 0, 0),  // East
                playerFeetPos.add(-1, 0, 0), // West
                playerFeetPos.add(0, 0, 1),  // South
                playerFeetPos.add(0, 0, -1)  // North
            };
        }

        for (BlockPos pos : positions) {
            if (canPlaceAt(pos) && hasReachableAdjacentBlock(pos)) {
                return pos;
            }
        }

        return null;
    }

    private void swapBackToOriginal() {
        if (originalSlot != -1) {
            // Check if we're not already on the original slot
            FindItemResult currentSlotResult = InvUtils.findInHotbar(mc.player.getMainHandStack().getItem());
            int currentSlot = currentSlotResult.found() ? currentSlotResult.slot() : -1;

            if (currentSlot != originalSlot) {
                InvUtils.swap(originalSlot, false);
            }
        }
        originalSlot = -1;
    }

    private boolean willAffectPlayer(BlockPos pos) {
        if (!selfProtect.get()) return false;

        // Get player's bounding box
        Vec3d playerPos = mc.player.getPos();
        double playerWidth = 0.6; // Player width
        double playerHeight = 1.8; // Player height

        // Create player bounding box
        double minX = playerPos.x - playerWidth / 2;
        double maxX = playerPos.x + playerWidth / 2;
        double minY = playerPos.y;
        double maxY = playerPos.y + playerHeight;
        double minZ = playerPos.z - playerWidth / 2;
        double maxZ = playerPos.z + playerWidth / 2;

        // Check if web position intersects with player
        double webMinX = pos.getX();
        double webMaxX = pos.getX() + 1.0;
        double webMinY = pos.getY();
        double webMaxY = pos.getY() + 1.0;
        double webMinZ = pos.getZ();
        double webMaxZ = pos.getZ() + 1.0;

        // Check for intersection
        boolean intersects = webMinX < maxX && webMaxX > minX &&
                           webMinY < maxY && webMaxY > minY &&
                           webMinZ < maxZ && webMaxZ > minZ;

        if (intersects) {
            if (debugMode.get()) {
                
            }
            return true;
        }

        // Also check positions adjacent to player (safety margin)
        double safetyMargin = 1.2;
        double safeMinX = playerPos.x - safetyMargin;
        double safeMaxX = playerPos.x + safetyMargin;
        double safeMinZ = playerPos.z - safetyMargin;
        double safeMaxZ = playerPos.z + safetyMargin;

        boolean tooClose = webMinX < safeMaxX && webMaxX > safeMinX &&
                          webMinZ < safeMaxZ && webMaxZ > safeMinZ &&
                          Math.abs(webMinY - playerPos.y) < 2.0;

        if (tooClose) {
            if (debugMode.get()) {
                
            }
            return true;
        }

        return false;
    }

    private boolean canPlaceAt(BlockPos pos) {
        // Check if position is within range
        if (!mc.player.getBlockPos().isWithinDistance(pos, range.get())) {
            return false;
        }

        // Check if block is air and can be replaced
        BlockState state = mc.world.getBlockState(pos);
        return state.isAir() ||
               state.getBlock() == Blocks.WATER ||
               state.getBlock() == Blocks.LAVA;
    }

    private boolean hasReachableAdjacentBlock(BlockPos pos) {
        // Check if there's at least one solid adjacent block that we can place against
        Direction[] directions = {Direction.NORTH, Direction.SOUTH, Direction.EAST, 
                                Direction.WEST, Direction.UP, Direction.DOWN};
        
        for (Direction dir : directions) {
            BlockPos adjacent = pos.offset(dir);
            BlockState state = mc.world.getBlockState(adjacent);
            
            // Check if the adjacent block is solid and we can reach it
            if (!state.isAir() && 
                state.getBlock() != Blocks.WATER && 
                state.getBlock() != Blocks.LAVA &&
                mc.player.getEyePos().isInRange(Vec3d.ofCenter(adjacent), range.get())) {
                return true;
            }
        }
        
        return false;
    }

    @Override
    public void onDeactivate() {
        timer = 0;
        targetBlock = null;
        isAiming = false;
        lastFrameTime = System.nanoTime();
        lastCombatTime = 0;
        webbedPlayers.clear();
        swapBackToOriginal(); // Ensure we swap back when module is disabled
    }

    // Add a method to check if another module has taken action this tick
    private boolean hasAnotherModuleTakenAction() {
        // Check if MaceAura has taken action
        try {
            Class<?> maceAuraClass = Class.forName("com.example.addon.modules.MaceAura");
            java.lang.reflect.Field actionTakenField = maceAuraClass.getDeclaredField("actionTakenThisTick");
            actionTakenField.setAccessible(true);
            return actionTakenField.getBoolean(null);
        } catch (Exception e) {
            // If we can't check, assume no conflict
            return false;
        }
    }

    // Mark that this module is taking action this tick
    private void markActionTaken() {
        try {
            Class<?> maceAuraClass = Class.forName("com.example.addon.modules.MaceAura");
            java.lang.reflect.Field actionTakenField = maceAuraClass.getDeclaredField("actionTakenThisTick");
            actionTakenField.setAccessible(true);
            actionTakenField.setBoolean(null, true);
        } catch (Exception e) {
            // Ignore if we can't set the flag
        }
    }
}