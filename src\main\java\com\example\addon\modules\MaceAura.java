package com.example.addon.modules;

import com.example.addon.AddonTemplate;
import com.example.addon.utils.EnchantmentUtils;
import com.example.addon.utils.SmoothAimingUtils;
import meteordevelopment.meteorclient.events.render.Render3DEvent;
import meteordevelopment.meteorclient.events.render.Render2DEvent;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.systems.modules.Modules;
import meteordevelopment.meteorclient.utils.entity.TargetUtils;

import meteordevelopment.meteorclient.utils.entity.SortPriority;
import meteordevelopment.meteorclient.utils.player.FindItemResult;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.client.MinecraftClient;
import net.minecraft.component.DataComponentTypes;
import net.minecraft.component.type.FireworksComponent;
import net.minecraft.enchantment.Enchantments;
import net.minecraft.entity.Entity;
import net.minecraft.entity.LivingEntity;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import net.minecraft.util.Hand;
import net.minecraft.util.math.Box;
import net.minecraft.util.math.Vec3d;
import net.minecraft.util.math.BlockPos;
import net.minecraft.block.BlockState;
import net.minecraft.client.option.KeyBinding;
import net.minecraft.client.util.math.MatrixStack;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.font.TextRenderer;
import com.mojang.blaze3d.systems.RenderSystem;

import java.util.function.Predicate;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

import static meteordevelopment.meteorclient.MeteorClient.mc;

public class MaceAura extends Module {
   private enum AttackMode {
       Packet,
       KeyPress
   }

   private enum AttackType {
       Spam,
       Timed
   }

   private final SettingGroup sgGeneral = settings.getDefaultGroup();
   private final SettingGroup sgElytraSwap = settings.createGroup("Elytra Swap");

   private final Setting<Boolean> autoTakeOffElytra = sgGeneral.add(new BoolSetting.Builder()
       .name("auto-take-off-elytra").description("Automatically take off elytra when conditions are met.").defaultValue(false).build());

   private final Setting<AttackMode> attackMode = sgGeneral.add(new EnumSetting.Builder<AttackMode>()
       .name("attack-mode")
       .description("The method used to attack the target.")
       .defaultValue(AttackMode.Packet)
       .build()
   );

   private final Setting<AttackType> attackType = sgGeneral.add(new EnumSetting.Builder<AttackType>()
       .name("attack-type")
       .description("The type of attack to perform.")
       .defaultValue(AttackType.Spam)
       .build()
   );
   private final Setting<Double> preSwapDistance = sgElytraSwap.add(new DoubleSetting.Builder()
    .name("pre-swap-distance")
    .description("Distance from ground to start holding chestplate.")
    .defaultValue(10.0)
    .min(3.0)
    .max(20.0)
    .sliderMin(3.0)
    .sliderMax(20.0)
    .visible(autoTakeOffElytra::get)
    .build()
);

private final Setting<Double> interactDistance = sgElytraSwap.add(new DoubleSetting.Builder()
    .name("interact-distance")
    .description("Distance from ground to interact with chestplate (equip it).")
    .defaultValue(3.0)
    .min(1.0)
    .max(10.0)
    .sliderMin(1.0)
    .sliderMax(10.0)
    .visible(autoTakeOffElytra::get)
    .build()
);
   private final Setting<Double> aimSpeed = sgGeneral.add(new DoubleSetting.Builder()
       .name("aim-speed").description("The speed at which the player aims at the target.").defaultValue(0.5d).range(0.1d, 1.0d).build());
   private final Setting<Boolean> smoothAiming = sgGeneral.add(new BoolSetting.Builder()
       .name("smooth-aiming").description("Smoothly aims at the target.").defaultValue(false).build());
   private final Setting<Double> smoothingFactor = sgGeneral.add(new DoubleSetting.Builder()
       .name("smoothing-factor").description("The smoothing factor for aim (0.0 to 1.0, higher = smoother).").defaultValue(0.5d).range(0.0d, 1.0d).sliderRange(0.0d, 1.0d).visible(smoothAiming::get).build());
   private final Setting<Double> randomOffsetMagnitude = sgGeneral.add(new DoubleSetting.Builder()
       .name("random-offset-magnitude").description("The magnitude of random offset applied to yaw and pitch.").defaultValue(0.0d).range(0.0d, 1.0d).sliderRange(0.0d, 1.0d).visible(smoothAiming::get).build());
   private final Setting<Boolean> fovCheck = sgGeneral.add(new BoolSetting.Builder()
       .name("fov-check").description("Only targets entities in the player's FOV.").defaultValue(false).build());
   private final Setting<Double> fov = sgGeneral.add(new DoubleSetting.Builder()
       .name("fov").description("The FOV in degrees to check for targets.").defaultValue(90.0d).range(1.0d, 180.0d).sliderRange(1.0d, 180.0d).visible(fovCheck::get).build());
   private final Setting<Integer> cps = sgGeneral.add(new IntSetting.Builder()
       .name("cps").description("Clicks per second.").defaultValue(20).range(1, 30).sliderRange(1, 30).visible(() -> attackType.get() == AttackType.Spam).build());
   private final Setting<Double> aimRange = sgGeneral.add(new DoubleSetting.Builder()
       .name("aim-range").description("Maximum distance to start aiming at targets.").defaultValue(15.0d).range(3.0d, 30.0d).sliderRange(3.0d, 30.0d).build());
   private final Setting<Double> attackReach = sgGeneral.add(new DoubleSetting.Builder()
       .name("attack-reach").description("Maximum distance to attack targets.").defaultValue(4.5d).range(2.0d, 6.0d).sliderRange(2.0d, 6.0d).build());
   private final Setting<Boolean> attributeSwap = sgGeneral.add(new BoolSetting.Builder()
       .name("attribute-swap").description("Whether to swap between density and breach maces.").defaultValue(true).build());
   private final Setting<Double> minFallDistance = sgGeneral.add(new DoubleSetting.Builder()
       .name("min-fall-distance").description("Minimum fall distance to trigger density mace swap.").defaultValue(3.0).min(0.0).sliderMin(0.0).visible(attributeSwap::get).build());
   private final Setting<Boolean> autoDetectMaces = sgGeneral.add(new BoolSetting.Builder()
       .name("auto-detect-maces").description("Automatically detects mace slots based on enchantments.").defaultValue(true).visible(attributeSwap::get).build());
   private final Setting<Integer> densityMaceSlot = sgGeneral.add(new IntSetting.Builder()
       .name("density-mace-slot").description("The slot of the density enchanted mace.").defaultValue(1).range(1, 9).sliderRange(1, 9).visible(() -> attributeSwap.get() && !autoDetectMaces.get()).build());
   private final Setting<Integer> breachMaceSlot = sgGeneral.add(new IntSetting.Builder()
       .name("breach-mace-slot").description("The slot of the breach enchanted mace.").defaultValue(2).range(1, 9).sliderRange(1, 9).visible(() -> attributeSwap.get() && !autoDetectMaces.get()).build());
   private final Setting<Integer> swapDelay = sgGeneral.add(new IntSetting.Builder()
       .name("swap-delay").description("Delay before swapping weapons.").defaultValue(2).range(0, 20).sliderRange(0, 20).visible(attributeSwap::get).build());
   private final Setting<Integer> swapBackDelay = sgGeneral.add(new IntSetting.Builder()
       .name("swap-back-delay").description("Delay before swapping back to original weapon.").defaultValue(2).range(0, 20).sliderRange(0, 20).visible(attributeSwap::get).build());
   private final Setting<Integer> minCharge = sgGeneral.add(new IntSetting.Builder()
       .name("min-charge").description("Minimum charge percentage (0-100) required for attribute swapping. At 0, no charge is waited for; at 100, full charge is waited for.").defaultValue(0).range(0, 100).sliderRange(0, 100).build());



   private final Setting<Boolean> takeOffOnGround = sgGeneral.add(new BoolSetting.Builder()
       .name("take-off-on-ground").description("Take off elytra when enemy is on ground.").defaultValue(true).visible(autoTakeOffElytra::get).build());
   private final Setting<Boolean> takeOffNoFirework = sgGeneral.add(new BoolSetting.Builder()
       .name("take-off-no-firework").description("Take off elytra when enemy is not holding firework or wind charge.").defaultValue(true).visible(autoTakeOffElytra::get).build());
   private final Setting<Double> groundCheckDistance = sgGeneral.add(new DoubleSetting.Builder()
       .name("ground-check-distance").description("Distance from ground to trigger elytra swap and attack.").defaultValue(0.5).range(0.0, 5.0).sliderRange(0.0, 5.0).build());
   private final Setting<Double> elytraTakeOffDistance = sgGeneral.add(new DoubleSetting.Builder()
       .name("elytra-take-off-distance").description("Distance from attack range to take off elytra.").defaultValue(3.0).range(0.0, 10.0).sliderRange(0.0, 10.0).visible(autoTakeOffElytra::get).build());
   private final Setting<Boolean> waitLastMoment = sgGeneral.add(new BoolSetting.Builder()
       .name("wait-last-moment").description("Wait until the last possible moment to attack.").defaultValue(true).build());
   private final Setting<Boolean> detailedLogging = sgGeneral.add(new BoolSetting.Builder()
       .name("detailed-logging").description("Enable detailed logging for debugging.").defaultValue(false).build());
   private final Setting<Boolean> disableOnElytra = sgGeneral.add(new BoolSetting.Builder()
       .name("disable-on-elytra").description("Disables MaceAura when the player is flying with an elytra.").defaultValue(true).build());
   private final Setting<Boolean> dontLookUp = sgGeneral.add(new BoolSetting.Builder()
       .name("dont-look-up").description("Prevents aiming upward when targeting entities.").defaultValue(false).build());
   private final Setting<Boolean> swapToElytraAfterAttack = sgGeneral.add(new BoolSetting.Builder()
       .name("swap-to-elytra-after-attack").description("Automatically swaps back to elytra after attacking.").defaultValue(false).build());

   private final Setting<Integer> postAttackWaitTicks = sgGeneral.add(new IntSetting.Builder()
       .name("post-attack-wait-ticks")
       .description("Ticks to wait after attack before swapping to elytra.")
       .defaultValue(5)
       .min(0)
       .max(20)
       .build());

   private LivingEntity target = null;
   private int originalSlot = -1;
   private int swapBackTimer = -1;
   private long lastClickTime = 0;
   private Targets targetsModule;
   private double distanceToTarget;

   private boolean isAttackingFlag = false;
   private long lastAttackTimeMillis = 0;

   private final Setting<Integer> elytraSwapDelayMillis = sgElytraSwap.add(new IntSetting.Builder()
       .name("swap-delay-millis").description("Delay (in milliseconds) before swapping to the target item for elytra swap.").defaultValue(50).min(0).max(1000).build());
   private final Setting<Integer> elytraInteractDelayMillis = sgElytraSwap.add(new IntSetting.Builder()
       .name("interact-delay-millis").description("Delay (in milliseconds) after swapping and before interacting (equipping) for elytra swap.").defaultValue(50).min(0).max(1000).build());
   private final Setting<Integer> elytraSwapBackDelayMillis = sgElytraSwap.add(new IntSetting.Builder()
       .name("swap-back-delay-millis").description("Delay (in milliseconds) after interacting and before swapping back to original slot for elytra swap.").defaultValue(50).min(0).max(1000).build());

   private long elytraSwapTimerMillis = -1;
    private long lastElytraSwapTime = 0;
    private int elytraSwapState = 0;
    private FindItemResult targetElytraItem;
    private int originalElytraHotbarSlot = -1;
    
    private int postAttackWaitTimer = -1;
    private boolean waitingForPostAttackSwap = false;
   private boolean elytraSwapInProgress = false;

   private int currentSwapDelay = 0;

   private boolean preSwapActive = false;
   private int preSwapSlot = -1;
   private int originalPreSwapSlot = -1;

   private Method doAttackMethod = null;
   private WorldToScreenUtil worldToScreenUtil = new WorldToScreenUtil();

   private static class PhysicsResult {
       double ticksToGround;
       double ticksToOutOfRange;

       PhysicsResult(double ticksToGround, double ticksToOutOfRange) {
           this.ticksToGround = ticksToGround;
           this.ticksToOutOfRange = ticksToOutOfRange;
       }
   }

   private class WorldToScreenUtil {
       public Vec3d worldToScreen(Vec3d worldPos) {
           if (mc.player == null || mc.gameRenderer == null) return null;
           
           Vec3d cameraPos = mc.gameRenderer.getCamera().getPos();
           MatrixStack matrices = new MatrixStack();
           
           // Simple projection - this is a basic implementation
           double deltaX = worldPos.x - cameraPos.x;
           double deltaY = worldPos.y - cameraPos.y;
           double deltaZ = worldPos.z - cameraPos.z;
           
           float yaw = mc.player.getYaw();
           float pitch = mc.player.getPitch();
           
           // Apply rotation
           double cosYaw = Math.cos(Math.toRadians(yaw + 90));
           double sinYaw = Math.sin(Math.toRadians(yaw + 90));
           double cosPitch = Math.cos(Math.toRadians(-pitch));
           double sinPitch = Math.sin(Math.toRadians(-pitch));
           
           double rotatedX = deltaX * cosYaw + deltaZ * sinYaw;
           double rotatedZ = deltaX * -sinYaw + deltaZ * cosYaw;
           double rotatedY = deltaY * cosPitch - rotatedZ * sinPitch;
           double finalZ = deltaY * sinPitch + rotatedZ * cosPitch;
           
           if (finalZ <= 0) return null; // Behind camera
           
           // Project to screen
           double fov = mc.options.getFov().getValue();
           double screenScale = mc.getWindow().getScaledHeight() / (2.0 * Math.tan(Math.toRadians(fov / 2)));
           
           double screenX = mc.getWindow().getScaledWidth() / 2.0 + (rotatedX * screenScale) / finalZ;
           double screenY = mc.getWindow().getScaledHeight() / 2.0 - (rotatedY * screenScale) / finalZ;
           
           return new Vec3d(screenX, screenY, finalZ);
       }
   }




   @Override
   public void onActivate() {
       super.onActivate();
       if (!hasMaceInHotbar()) {
           warning("No mace found in hotbar. Deactivating MaceAura.");
           toggle();
           return;
       }
       if (disableOnElytra.get() && mc.player != null && mc.player.getAbilities().flying) {
           warning("MaceAura disabled while elytra flying.");
           toggle();
       }
   }

   public MaceAura() {
       super(AddonTemplate.CATEGORY, "mace-aura", "Automatically aims and attacks with maces.");
       targetsModule = Modules.get().get(Targets.class);

       try {
           doAttackMethod = MinecraftClient.class.getDeclaredMethod("method_1536");
           doAttackMethod.setAccessible(true);
       } catch (NoSuchMethodException e) {
           error("Failed to find Minecraft's attack method via reflection. (method_1536). " +
                 "Consider updating the obfuscated method name for your Minecraft version or " +
                 "using an alternative attack method if available in Meteor Client's API.");
           e.printStackTrace();
       }
   }

   public boolean isAttacking() {
       return isAttackingFlag;
   }

   public long lastAttackTime() {
       return lastAttackTimeMillis;
   }

   private boolean hasDensityEnchantment(ItemStack stack) {
       return EnchantmentUtils.hasEnchantment(stack, Enchantments.DENSITY);
   }

   private boolean hasBreachEnchantment(ItemStack stack) {
       return EnchantmentUtils.hasEnchantment(stack, Enchantments.BREACH);
   }

   private boolean isTargetInFOV(Entity entity) {
       double yaw = mc.player.getYaw();
       double pitch = mc.player.getPitch();

       Vec3d playerLookVec = mc.player.getRotationVec(1.0F);
       Vec3d targetVec = entity.getPos().subtract(mc.player.getEyePos()).normalize();

       double angle = Math.toDegrees(Math.acos(playerLookVec.dotProduct(targetVec)));

       return angle <= fov.get() / 2.0;
   }

   private void findMaces() {
       if (mc.player == null) return;

       int foundDensityMaceSlot = -1;
       int foundBreachMaceSlot = -1;

       for (int i = 0; i < 9; i++) { // Iterate through hotbar slots
           ItemStack stack = mc.player.getInventory().getStack(i);
           if (stack.getItem() == Items.MACE) { // Check if it's a mace
               if (hasDensityEnchantment(stack)) {
                   foundDensityMaceSlot = i;
               } else if (hasBreachEnchantment(stack)) {
                   foundBreachMaceSlot = i;
               }
           }
       }

       if (foundDensityMaceSlot != -1) {
           densityMaceSlot.set(foundDensityMaceSlot + 1);
       }
       if (foundBreachMaceSlot != -1) {
           breachMaceSlot.set(foundBreachMaceSlot + 1);
       }
   }

   private boolean hasMaceInHotbar() {
       for (int i = 0; i < 9; i++) {
           ItemStack stack = mc.player.getInventory().getStack(i);
           if (stack.getItem() == Items.MACE) {
               return true;
           }
       }
       return false;
   }

   private void log(String message) {
       if (detailedLogging.get()) {
           info(message);
       }
   }

   private long getClickDelayMillis() {
       return 1000 / cps.get();
   }

   private int getFireworkDuration(ItemStack fireworkStack) {
       if (fireworkStack == null || fireworkStack.getItem() != Items.FIREWORK_ROCKET) {
           return 0;
       }
       FireworksComponent fireworksComponent = fireworkStack.get(DataComponentTypes.FIREWORKS);
       if (fireworksComponent != null) {
           return fireworksComponent.flightDuration();
       }
       return 0;
   }



private boolean shouldTakeOffElytra() {
    if (!autoTakeOffElytra.get() || target == null || elytraSwapInProgress) return false;
    ItemStack equippedChest = mc.player.getInventory().getStack(38);
    if (equippedChest.getItem() != Items.ELYTRA) return false;

    double distanceToGround = mc.player.getPos().y - findGroundLevel(mc.player.getPos());
    this.distanceToTarget = mc.player.distanceTo(target);

    if (distanceToGround <= interactDistance.get() && distanceToTarget <= attackReach.get() + 5.0) {
        boolean conditionMet = true;
        if (takeOffOnGround.get() && !target.isOnGround()) conditionMet = false;
        if (takeOffNoFirework.get()) {
            ItemStack mainHand = target.getMainHandStack();
            ItemStack offHand = target.getOffHandStack();
            int fireworkDuration = 0;
            if (mainHand.getItem() == Items.FIREWORK_ROCKET) fireworkDuration = getFireworkDuration(mainHand);
            else if (offHand.getItem() == Items.FIREWORK_ROCKET) fireworkDuration = getFireworkDuration(offHand);
            boolean hasWindCharge = mainHand.getItem() == Items.WIND_CHARGE || offHand.getItem() == Items.WIND_CHARGE;
            if (fireworkDuration > 0 || hasWindCharge) conditionMet = false;
        }
        return conditionMet;
    }

    return false;
}

private void handleElytraPreSwap() {
    if (!autoTakeOffElytra.get() || target == null) {
        if (preSwapActive) {
            if (originalPreSwapSlot != -1) {
                InvUtils.swap(originalPreSwapSlot, false);
                log("Swapped back to original weapon - pre-swap cancelled.");
            }
            preSwapActive = false;
            preSwapSlot = -1;
            originalPreSwapSlot = -1;
            log("Deactivating pre-swap: target lost or module disabled.");
        }
        return;
    }

    ItemStack equippedChest = mc.player.getInventory().getStack(38);
    if (equippedChest.getItem() != Items.ELYTRA) {
        if (preSwapActive) {
            if (originalPreSwapSlot != -1) {
                InvUtils.swap(originalPreSwapSlot, false);
                log("Swapped back to original weapon - elytra not equipped.");
            }
            preSwapActive = false;
            preSwapSlot = -1;
            originalPreSwapSlot = -1;
            log("Deactivating pre-swap: elytra not equipped.");
        }
        return;
    }

    double distanceToTarget = mc.player.distanceTo(target);
    double distanceToGround = mc.player.getPos().y - findGroundLevel(mc.player.getPos());

    // Check if targetsModule is available
    if (targetsModule == null || !targetsModule.isActive()) {
        log("Cannot perform pre-swap: Targets module not available.");
        return;
    }

    boolean shouldPreSwap = distanceToTarget <= targetsModule.range.get() &&
                           distanceToGround >= preSwapDistance.get() &&
                           mc.player.fallDistance > 1.0 &&
                           !elytraSwapInProgress;

    if (shouldPreSwap && !preSwapActive) {
        FindItemResult chestplateItem = InvUtils.findInHotbar(itemStack ->
            itemStack.getItem() == Items.DIAMOND_CHESTPLATE ||
            itemStack.getItem() == Items.NETHERITE_CHESTPLATE ||
            itemStack.getItem() == Items.IRON_CHESTPLATE ||
            itemStack.getItem() == Items.CHAINMAIL_CHESTPLATE ||
            itemStack.getItem() == Items.GOLDEN_CHESTPLATE ||
            itemStack.getItem() == Items.LEATHER_CHESTPLATE
        );

        if (chestplateItem.found()) {
            FindItemResult originalSlotResult = InvUtils.findInHotbar(mc.player.getMainHandStack().getItem());
            originalPreSwapSlot = originalSlotResult.found() ? originalSlotResult.slot() : -1;

            InvUtils.swap(chestplateItem.slot(), false);

            preSwapActive = true;
            preSwapSlot = chestplateItem.slot();
            log(String.format("Pre-swapped to chestplate at %.1f blocks above ground.", distanceToGround));
        } else {
            log("No chestplate found in hotbar for pre-swap.");
        }
    } else if (preSwapActive && targetsModule != null && targetsModule.isActive() && distanceToTarget > targetsModule.range.get()) {
        if (originalPreSwapSlot != -1) {
            InvUtils.swap(originalPreSwapSlot, false);
            log("Swapped back to original weapon - target too far.");
        }
        preSwapActive = false;
        preSwapSlot = -1;
        originalPreSwapSlot = -1;
        log("Deactivating pre-swap: target too far.");
    }
}

   private double calculateTimeToImpact() {
       if (target == null) return Double.MAX_VALUE;

       Vec3d playerPos = mc.player.getPos();
       Vec3d targetPos = target.getPos();
       Vec3d velocity = mc.player.getVelocity();

       double distanceAlongPath = playerPos.distanceTo(targetPos);
       double currentSpeed = velocity.length();

       int predictedFireworkDuration = 0;
       if (mc.player.getMainHandStack().getItem() == Items.FIREWORK_ROCKET) {
           predictedFireworkDuration = getFireworkDuration(mc.player.getMainHandStack());
       } else if (mc.player.getOffHandStack().getItem() == Items.FIREWORK_ROCKET) {
           predictedFireworkDuration = getFireworkDuration(mc.player.getOffHandStack());
       }

       if (predictedFireworkDuration > 0) {
           double fireworkBoostTicks = predictedFireworkDuration * 20.0;
           double estimatedBoostDistance = fireworkBoostTicks * 0.5;
           distanceAlongPath -= estimatedBoostDistance;
       }

       double estimatedSpeed = Math.max(currentSpeed, 1.0);
       return distanceAlongPath / estimatedSpeed;
   }

   private void startElytraSwap() {
    if (mc.player == null) return;

    ItemStack equippedChest = mc.player.getInventory().getStack(38);

    if (preSwapActive && equippedChest.getItem() == Items.ELYTRA) {
        elytraSwapState = 5;
        elytraSwapTimerMillis = elytraInteractDelayMillis.get();
        lastElytraSwapTime = System.currentTimeMillis();
        elytraSwapInProgress = true;

        log("Starting pre-swapped elytra removal sequence.");
        return;
    }

    FindItemResult targetItemToFind = null;
    boolean isEquippedChestplate = equippedChest.getItem() == Items.DIAMOND_CHESTPLATE ||
                                   equippedChest.getItem() == Items.NETHERITE_CHESTPLATE ||
                                   equippedChest.getItem() == Items.IRON_CHESTPLATE ||
                                   equippedChest.getItem() == Items.CHAINMAIL_CHESTPLATE ||
                                   equippedChest.getItem() == Items.GOLDEN_CHESTPLATE ||
                                   equippedChest.getItem() == Items.LEATHER_CHESTPLATE;

    if (equippedChest.getItem() == Items.ELYTRA) {
        targetItemToFind = InvUtils.findInHotbar(itemStack ->
            itemStack.getItem() == Items.DIAMOND_CHESTPLATE ||
            itemStack.getItem() == Items.NETHERITE_CHESTPLATE ||
            itemStack.getItem() == Items.IRON_CHESTPLATE ||
            itemStack.getItem() == Items.CHAINMAIL_CHESTPLATE ||
            itemStack.getItem() == Items.GOLDEN_CHESTPLATE ||
            itemStack.getItem() == Items.LEATHER_CHESTPLATE
        );
    } else if (isEquippedChestplate) {
        targetItemToFind = InvUtils.findInHotbar(Items.ELYTRA);
    } else {
        targetItemToFind = InvUtils.findInHotbar(Items.ELYTRA);
        if (!targetItemToFind.found()) {
            targetItemToFind = InvUtils.findInHotbar(itemStack ->
                itemStack.getItem() == Items.DIAMOND_CHESTPLATE ||
                itemStack.getItem() == Items.NETHERITE_CHESTPLATE ||
                itemStack.getItem() == Items.IRON_CHESTPLATE ||
                itemStack.getItem() == Items.CHAINMAIL_CHESTPLATE ||
                itemStack.getItem() == Items.GOLDEN_CHESTPLATE ||
                itemStack.getItem() == Items.LEATHER_CHESTPLATE
            );
        }
    }

    if (targetItemToFind != null && targetItemToFind.found()) {
        this.targetElytraItem = targetItemToFind;
        FindItemResult originalSlotResult = InvUtils.findInHotbar(mc.player.getMainHandStack().getItem());
        originalElytraHotbarSlot = originalSlotResult.found() ? originalSlotResult.slot() : -1;

        elytraSwapState = 1;
        elytraSwapTimerMillis = elytraSwapDelayMillis.get();
        lastElytraSwapTime = System.currentTimeMillis();
        elytraSwapInProgress = true;
        log("Starting regular elytra/chestplate swap process.");
    } else {
        log("No suitable item (elytra or chestplate) found in hotbar to initiate swap.");
    }
}

private void handlePostAttackWait() {
        if (!waitingForPostAttackSwap) return;
        
        if (postAttackWaitTimer > 0) {
            postAttackWaitTimer--;
        } else {
            waitingForPostAttackSwap = false;
            
            // Now perform the elytra swap
            ItemStack equippedChest = mc.player.getInventory().getStack(38);
            if (equippedChest.getItem() != Items.ELYTRA) {
                FindItemResult elytraItem = InvUtils.findInHotbar(Items.ELYTRA);
                if (elytraItem.found()) {
                    FindItemResult originalSlotResult = InvUtils.findInHotbar(mc.player.getMainHandStack().getItem());
                    originalElytraHotbarSlot = originalSlotResult.found() ? originalSlotResult.slot() : -1;
                    targetElytraItem = elytraItem;
                    
                    elytraSwapState = 1;
                    elytraSwapTimerMillis = elytraSwapDelayMillis.get();
                    lastElytraSwapTime = System.currentTimeMillis();
                    elytraSwapInProgress = true;
                    
                    log("Starting elytra swap after post-attack wait.");
                } else {
                    log("No elytra found in hotbar for post-attack swap.");
                }
            }
        }
    }

    private void handleElytraSwap() {
        if (!elytraSwapInProgress) return;

        long currentTime = System.currentTimeMillis();
        long elapsed = currentTime - lastElytraSwapTime;

        if (elapsed < elytraSwapTimerMillis) {
            return;
        }

        lastElytraSwapTime = currentTime;

        if (elytraSwapState == 1) {
            if (targetElytraItem != null && targetElytraItem.found()) {
                InvUtils.swap(targetElytraItem.slot(), false);
                elytraSwapState = 2;
                elytraSwapTimerMillis = elytraInteractDelayMillis.get();
            } else {
                log("Error: Target item not found for swap state 1. Aborting swap.");
                elytraSwapInProgress = false;
            }
        } else if (elytraSwapState == 2) {
            mc.interactionManager.interactItem(mc.player, Hand.MAIN_HAND);
            log("Interacted with item (equipped).");
            elytraSwapState = 3;
            elytraSwapTimerMillis = elytraSwapBackDelayMillis.get();
        } else if (elytraSwapState == 3) {
            if (originalElytraHotbarSlot != -1) {
                InvUtils.swap(originalElytraHotbarSlot, false);
                log("Swapped back to original slot.");
            } else {
                log("Warning: Original hotbar slot was -1. Could not swap back to original item.");
            }
            elytraSwapState = 0;
            elytraSwapTimerMillis = -1;
            originalElytraHotbarSlot = -1;
            targetElytraItem = null;
            elytraSwapInProgress = false;
            log("Elytra/chestplate swap complete.");
        } else if (elytraSwapState == 4) {
            if (originalPreSwapSlot != -1) {
                InvUtils.swap(originalPreSwapSlot, false);
                log("Swapped back to original weapon after instant equip.");
            }
            elytraSwapState = 0;
            elytraSwapTimerMillis = -1;
            originalPreSwapSlot = -1;
            elytraSwapInProgress = false;
            log("Instant elytra swap complete.");
        } else if (elytraSwapState == 5) {
            mc.interactionManager.interactItem(mc.player, Hand.MAIN_HAND);
            log("Equipped chestplate from pre-swap.");
            elytraSwapState = 6;
            elytraSwapTimerMillis = elytraSwapBackDelayMillis.get();
        } else if (elytraSwapState == 6) {
            if (originalPreSwapSlot != -1) {
                InvUtils.swap(originalPreSwapSlot, false);
                log("Swapped back to sword after chestplate equip.");
            }

            preSwapActive = false;
            preSwapSlot = -1;
            originalPreSwapSlot = -1;

            elytraSwapState = 0;
            elytraSwapTimerMillis = -1;
            elytraSwapInProgress = false;

            log("Pre-swap sequence complete. Ready for attribute swap.");
        }
    }
   private PhysicsResult calculateCollisionTiming() {
       if (target == null) return new PhysicsResult(Double.MAX_VALUE, Double.MAX_VALUE);

       Vec3d playerPos = mc.player.getPos();
       Vec3d playerVel = mc.player.getVelocity();
       Vec3d targetPos = target.getPos();
       Vec3d targetVel = target.getVelocity();

       double ticksToGround = calculateTicksToGround(playerPos, playerVel);
       double ticksToOutOfRange = calculateTicksToOutOfRange(playerPos, playerVel, targetPos, targetVel);

       return new PhysicsResult(ticksToGround, ticksToOutOfRange);
   }

   private double calculateTicksToGround(Vec3d pos, Vec3d velocity) {
       double gravity = 0.08;
       double drag = 0.98;

       double y = pos.y;
       double vy = velocity.y;
       double groundY = findGroundLevel(pos);

       if (y <= groundY) return 0;

       for (int tick = 0; tick < 200; tick++) {
           y += vy;
           vy = (vy - gravity) * drag;

           if (y <= groundY + groundCheckDistance.get()) {
               return tick + 1;
           }
       }

       return Double.MAX_VALUE;
   }

   private double calculateTicksToOutOfRange(Vec3d playerPos, Vec3d playerVel, Vec3d targetPos, Vec3d targetVel) {
       double gravity = 0.08;
       double drag = 0.98;

       Vec3d pPos = new Vec3d(playerPos.x, playerPos.y, playerPos.z);
       Vec3d pVel = new Vec3d(playerVel.x, playerVel.y, playerVel.z);
       Vec3d tPos = new Vec3d(targetPos.x, targetPos.y, targetPos.z);
       Vec3d tVel = new Vec3d(targetVel.x, targetVel.y, targetVel.z);

       for (int tick = 0; tick < 100; tick++) {
           pPos = pPos.add(pVel);
           pVel = new Vec3d(pVel.x * drag, (pVel.y - gravity) * drag, pVel.z * drag);

           tPos = tPos.add(tVel);
           if (!target.isOnGround()) {
               tVel = new Vec3d(tVel.x * drag, (tVel.y - gravity) * drag, tVel.z * drag);
           }

           double distance = pPos.distanceTo(tPos);
           if (distance > attackReach.get()) {
               return tick + 1;
           }
       }

       return Double.MAX_VALUE;
   }

   private double findGroundLevel(Vec3d pos) {
       for (int y = (int)pos.y; y >= mc.world.getBottomY(); y--) {
           BlockPos blockPos = new BlockPos((int)pos.x, y, (int)pos.z);
           BlockState blockState = mc.world.getBlockState(blockPos);

           if (!blockState.getCollisionShape(mc.world, blockPos).isEmpty()) {
               return y + 1;
           }
       }
       return mc.world.getBottomY();
   }

   @EventHandler
   private void onTick(TickEvent.Pre event) {
       if (disableOnElytra.get() && mc.player != null && mc.player.getAbilities().flying) {
           warning("MaceAura disabled while elytra flying.");
           toggle();
           return;
       }
       if (mc.player == null) return;

       isAttackingFlag = false;

       if (autoDetectMaces.get()) findMaces();
       handlePostAttackWait();
        handleElytraSwap();
       handleElytraPreSwap();

       if (swapBackTimer > 0) {
           swapBackTimer--;
       } else if (swapBackTimer == 0 && originalSlot != -1) {
           InvUtils.swap(originalSlot, true);
           swapBackTimer = -1;
           originalSlot = -1;
       }

       LivingEntity oldTarget = this.target;

       Targets targetsModule = Modules.get().get(Targets.class);
       if (targetsModule == null || !targetsModule.isActive()) {
           this.target = null;
           if (oldTarget != null) info("Lost target (Targets module not active).");
           return;
       }

       Predicate<Entity> targetPredicate = entity -> {
           if (entity.equals(mc.player)) return false;
           if (fovCheck.get() && !isTargetInFOV(entity)) return false;
           return targetsModule.entityFilter.get().contains(entity.getType());
       };

       Entity foundTarget = TargetUtils.get(targetPredicate, SortPriority.LowestDistance);

       if (foundTarget instanceof LivingEntity) {
           this.target = (LivingEntity) foundTarget;
       } else {
           this.target = null;
       }

       if (this.target != null && this.target != oldTarget) {
           info("New target acquired: %s (%.2f blocks away)", this.target.getName().getString(), mc.player.distanceTo(this.target));
       } else if (this.target == null && oldTarget != null) {
           info("Lost target.");
       }

       if (this.target == null) return;

       this.distanceToTarget = mc.player.distanceTo(this.target);

       if (shouldTakeOffElytra() && !elytraSwapInProgress) {
           log("Conditions met to take off elytra. Starting swap...");
           startElytraSwap();
       }

       boolean cpsReady = (System.currentTimeMillis() - lastClickTime >= getClickDelayMillis());
       boolean inRange = distanceToTarget <= attackReach.get();
       boolean fallDistanceMet = !attributeSwap.get() || mc.player.fallDistance >= minFallDistance.get();
       boolean isFalling = !mc.player.isOnGround();
       boolean isSwappingElytra = elytraSwapInProgress;
       float attackCooldownProgress = mc.player.getAttackCooldownProgress(0.0f);
       boolean fullyCharged;
       if (minCharge.get() == 0) {
           fullyCharged = true;
       } else {
           fullyCharged = attackCooldownProgress >= (minCharge.get() / 100.0f);
       }

       boolean mustAttackNow = false;
       String reason = "";

       if (!cpsReady || !fallDistanceMet || isSwappingElytra) {
           if (detailedLogging.get()) {
               StringBuilder debugReason = new StringBuilder(String.format("Not attacking %s. Reasons: ", target.getName().getString()));
               if (!cpsReady) debugReason.append("CPS not ready. ");
               if (!fallDistanceMet) debugReason.append("Fall distance too low. ");
               if (!isFalling) debugReason.append("Player is on the ground. ");
               if (isSwappingElytra) debugReason.append("Elytra swap in progress. ");
               log(debugReason.toString());
           }
           return;
       }

       if (currentSwapDelay > 0) {
           currentSwapDelay--;
           if (detailedLogging.get()) {
               log(String.format("Waiting for swap delay: %d ticks remaining.", currentSwapDelay));
           }
           return;
       }

       if (attackType.get() == AttackType.Timed) {

           PhysicsResult physics = calculateCollisionTiming();
           boolean aboutToHitGround = physics.ticksToGround <= 2;
           boolean targetLeavingRange = physics.ticksToOutOfRange <= 2;
           mustAttackNow = inRange && fullyCharged && (aboutToHitGround || targetLeavingRange);

           reason = fullyCharged ? "Fully charged" :
                   aboutToHitGround ? String.format("About to hit ground (%.1f ticks)", physics.ticksToGround) :
                   targetLeavingRange ? String.format("Target leaving range (%.1f ticks)", physics.ticksToOutOfRange) :
                   "Not in range or not last moment";
       } else if (waitLastMoment.get()) {
           PhysicsResult physics = calculateCollisionTiming();
           boolean aboutToHitGround = physics.ticksToGround <= 2;
           boolean targetLeavingRange = physics.ticksToOutOfRange <= 2;
           mustAttackNow = inRange && fullyCharged && (aboutToHitGround || targetLeavingRange);

           reason = fullyCharged ? "Fully charged" :
                   aboutToHitGround ? String.format("About to hit ground (%.1f ticks)", physics.ticksToGround) :
                   targetLeavingRange ? String.format("Target leaving range (%.1f ticks)", physics.ticksToOutOfRange) :
                   "Not in range or not last moment";
       } else {
           mustAttackNow = inRange && fullyCharged;
           reason = "Attack conditions met";
       }

       if (!mustAttackNow) {
           if (detailedLogging.get()) {
               log(String.format("Not attacking %s. Reason: %s", target.getName().getString(), reason));
           }
           return;
       }

       // Check if the attack cooldown is ready before proceeding with the attack, unless minCharge is 0 (meaning no charge is waited for).
       if (minCharge.get() > 0 && mc.player.getAttackCooldownProgress(0.0f) < 1.0f) {
           if (detailedLogging.get()) {
               log(String.format("Not attacking %s. Attack cooldown not ready.", target.getName().getString()));
           }
           return;
       }

       info("Executing attack on %s with %s mode. Reason: %s", target.getName().getString(), attackMode.get().name(), reason);

       if (swapToElytraAfterAttack.get()) {
           postAttackWaitTimer = postAttackWaitTicks.get();
           waitingForPostAttackSwap = true;
       }

       if (attributeSwap.get() && originalSlot == -1) {
           ItemStack mainHandStack = mc.player.getMainHandStack();
           boolean isHoldingMace = mainHandStack.getItem() == Items.MACE;

           if (!isHoldingMace) {
               FindItemResult findItemResult = InvUtils.findInHotbar(mainHandStack.getItem());
               if (findItemResult.found()) {
                   originalSlot = findItemResult.slot();
                   if (mc.player.fallDistance >= minFallDistance.get()) {
                       log("High fall distance. Swapping to Density mace.");
                       if (densityMaceSlot.get() - 1 != InvUtils.findInHotbar(mc.player.getMainHandStack().getItem()).slot()) {
                           InvUtils.swap(densityMaceSlot.get() - 1, false);
                       }
                   } else {
                       log("Low fall distance. Swapping to Breach mace.");
                       if (breachMaceSlot.get() - 1 != InvUtils.findInHotbar(mc.player.getMainHandStack().getItem()).slot()) {
                           InvUtils.swap(breachMaceSlot.get() - 1, false);
                       }
                   }
                   currentSwapDelay = swapDelay.get();
                   swapBackTimer = Math.max(swapBackDelay.get(), 1);
                   log(String.format("Initiated mace swap. Current swap delay: %d ticks.", currentSwapDelay));
               }
           } else {
               log("Already holding a mace. No swap needed.");
           }
       }

       switch (attackMode.get()) {
           case Packet -> {
                if (mc.options.attackKey != null) {
                    KeyBinding.onKeyPressed(mc.options.attackKey.getDefaultKey());
                    isAttackingFlag = true;
                    lastAttackTimeMillis = System.currentTimeMillis();
            }
        }
           case KeyPress -> {
                if (mc.options.attackKey != null) {
                    KeyBinding.onKeyPressed(mc.options.attackKey.getDefaultKey());
                    isAttackingFlag = true;
                    lastAttackTimeMillis = System.currentTimeMillis();
                } else {
                    error("KeyPress mode enabled, but 'attackKey' is null. This should not happen.");
                }
            }
       }
       lastClickTime = System.currentTimeMillis();
   }

   @EventHandler
   private void onRender3d(Render3DEvent event) {
       if (mc.player == null || target == null || mc.player.distanceTo(target) > aimRange.get()) {
           return;
       }

       Vec3d playerPos = mc.player.getEyePos();
        Box hitbox = target.getBoundingBox();
        Vec3d targetPos = hitbox.getCenter();

        if (targetPos.y > playerPos.y) {
            if (dontLookUp.get()) return;
        }

       double x = targetPos.x - playerPos.x;
       double y = targetPos.y - playerPos.y;
       double z = targetPos.z - playerPos.z;

       double distanceXZ = Math.sqrt(x * x + z * z);

       float targetYaw = (float) Math.toDegrees(Math.atan2(z, x)) - 90.0F;
       float targetPitch = (float) Math.toDegrees(-Math.atan2(y, distanceXZ));

       float currentYaw = mc.player.getYaw();
       float deltaYaw = targetYaw - currentYaw;
       if (deltaYaw > 180) deltaYaw -= 360;
       if (deltaYaw < -180) deltaYaw += 360;

       if (smoothAiming.get()) {
           float[] rotations = SmoothAimingUtils.calculateSmoothRotations(
               mc.player.getYaw(), mc.player.getPitch(),
               target.getPos().add(0, target.getHeight() / 2, 0), // Aim at center of target
               mc.player.getEyePos(),
               aimSpeed.get(),
               (float) (event.frameTime * 60), // Convert to ticks and cast to float
               smoothingFactor.get()
           );
           double randomYawOffset = (Math.random() - 0.5) * 2 * randomOffsetMagnitude.get();
           double randomPitchOffset = (Math.random() - 0.5) * 2 * randomOffsetMagnitude.get();

           mc.player.setYaw((float) (rotations[0] + randomYawOffset));
           mc.player.setPitch((float) (rotations[1] + randomPitchOffset));
       } else {
           double randomYawOffset = (Math.random() - 0.5) * 2 * randomOffsetMagnitude.get();
           double randomPitchOffset = (Math.random() - 0.5) * 2 * randomOffsetMagnitude.get();

           float frameAimSpeed = (float) (aimSpeed.get() * event.frameTime * 60);
           mc.player.setYaw((float) (currentYaw + deltaYaw * frameAimSpeed + randomYawOffset));

           float currentPitch = mc.player.getPitch();
           float deltaPitch = targetPitch - currentPitch;
           mc.player.setPitch((float) (currentPitch + deltaPitch * frameAimSpeed + randomPitchOffset));
       }
   }

   @EventHandler
   private void onRender2D(Render2DEvent event) {
       if (mc.player == null) return;

       // Unlock mouse cursor - using different approach for newer versions
       // Note: Perspective changes should be handled through proper game options
 
       // HUD rendering
       DrawContext context = event.drawContext;
       TextRenderer textRenderer = mc.textRenderer;
       
       int x = 10;
       int y = 10;
       
       // Background panel
       context.fill(x - 5, y - 5, x + 200, y + 120, 0x80000000);
       
       // Title
       context.drawTextWithShadow(textRenderer, "MaceAura HUD", x, y, 0xFFFFFF);
       y += 15;
       
       // Target information
       if (target != null) {
           context.drawTextWithShadow(textRenderer, "Target: " + target.getName().getString(), x, y, 0x00FF00);
           y += 10;
           context.drawTextWithShadow(textRenderer, String.format("Distance: %.1f blocks", distanceToTarget), x, y, 0x00FF00);
           y += 10;
           context.drawTextWithShadow(textRenderer, String.format("Health: %.1f/%.1f", 
               target instanceof LivingEntity ? ((LivingEntity)target).getHealth() : 0,
               target instanceof LivingEntity ? ((LivingEntity)target).getMaxHealth() : 0), x, y, 0x00FF00);
       } else {
           context.drawTextWithShadow(textRenderer, "No target", x, y, 0xFF0000);
       }
       y += 15;
       
       // Attack status
       context.drawTextWithShadow(textRenderer, "Attack Status:", x, y, 0xFFFF00);
       y += 10;
       
       boolean cpsReady = (System.currentTimeMillis() - lastClickTime >= getClickDelayMillis());
       context.drawTextWithShadow(textRenderer, "CPS Ready: " + (cpsReady ? "Yes" : "No"), x + 5, y, cpsReady ? 0x00FF00 : 0xFF0000);
       y += 10;
       
       boolean inRange = target != null && distanceToTarget <= attackReach.get();
       context.drawTextWithShadow(textRenderer, "In Range: " + (inRange ? "Yes" : "No"), x + 5, y, inRange ? 0x00FF00 : 0xFF0000);
       y += 10;
       
       boolean fallDistanceMet = !attributeSwap.get() || mc.player.fallDistance >= minFallDistance.get();
       context.drawTextWithShadow(textRenderer, "Fall Distance: " + String.format("%.1f", mc.player.fallDistance), x + 5, y, fallDistanceMet ? 0x00FF00 : 0xFF0000);
       y += 15;
       
       // Swap status
       context.drawTextWithShadow(textRenderer, "Swap Status:", x, y, 0xFFFF00);
       y += 10;
       context.drawTextWithShadow(textRenderer, "Elytra Swap: " + (elytraSwapInProgress ? "Active" : "Inactive"), x + 5, y, elytraSwapInProgress ? 0xFFFF00 : 0x00FF00);
       y += 10;
       context.drawTextWithShadow(textRenderer, "Post Attack Wait: " + (waitingForPostAttackSwap ? "Active" : "Inactive"), x + 5, y, waitingForPostAttackSwap ? 0xFFFF00 : 0x00FF00);
       y += 15;
       
       // Mouse cursor unlock indicator
       context.drawTextWithShadow(textRenderer, "Mouse: Unlocked", x, y, 0x00FFFF);
       
       // Crosshair overlay
       if (target != null && mc.currentScreen == null) {
           int centerX = mc.getWindow().getScaledWidth() / 2;
           int centerY = mc.getWindow().getScaledHeight() / 2;
           
           // Crosshair
           context.fill(centerX - 1, centerY - 5, centerX + 2, centerY - 2, 0xFFFFFFFF);
           context.fill(centerX - 1, centerY + 3, centerX + 2, centerY + 6, 0xFFFFFFFF);
           context.fill(centerX - 5, centerY - 1, centerX - 2, centerY + 2, 0xFFFFFFFF);
           context.fill(centerX + 3, centerY - 1, centerX + 6, centerY + 2, 0xFFFFFFFF);
           
           // Target indicator
           Vec3d targetScreenPos = worldToScreenUtil.worldToScreen(target.getPos().add(0, target.getHeight() / 2, 0));
           if (targetScreenPos != null) {
               int targetX = (int) targetScreenPos.x;
               int targetY = (int) targetScreenPos.y;
               
               context.fill(targetX - 2, targetY - 2, targetX + 3, targetY + 3, 0xFFFFFF00);
           }
       }
   }
}