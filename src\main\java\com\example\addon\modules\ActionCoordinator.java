package com.example.addon.modules;

import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.Queue;

import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.systems.modules.Modules;
import com.example.addon.AddonTemplate;
import com.example.addon.utils.RaycastUtils;
import net.minecraft.client.MinecraftClient;
import net.minecraft.entity.Entity;
import net.minecraft.util.hit.HitResult;
import net.minecraft.util.hit.EntityHitResult;
import net.minecraft.util.hit.BlockHitResult;

public class ActionCoordinator extends Module {
    private final Queue<Runnable> actionQueue = new ConcurrentLinkedQueue<>();
    private boolean isProcessing = false;
    private static final MinecraftClient mc = MinecraftClient.getInstance();

    public ActionCoordinator() {
        super(AddonTemplate.CATEGORY, "action-coordinator", "Coordinates actions to prevent anti-cheat flags.");
        // Initialize coordinator
    }

    public void scheduleAction(Runnable action) {
        actionQueue.offer(action);
        processQueue();
    }

    private synchronized void processQueue() {
        if (isProcessing) {
            return;
        }
        Runnable action = actionQueue.poll();
        if (action != null) {
            isProcessing = true;
            try {
                action.run();
            } finally {
                isProcessing = false;
                processQueue(); // Schedule next action immediately after current one finishes
            }
        }
    }

    /**
     * Central input validation for all player actions
     * @param actionType The type of action (ATTACK_ENTITY, ATTACK_BLOCK, etc.)
     * @param target The target entity (null for block actions)
     * @param blockPos The target block position (null for entity actions)
     * @return true if the action should be allowed, false if it should be blocked
     */
    public static boolean validateAction(ActionType actionType, Entity target, net.minecraft.util.math.BlockPos blockPos) {
        // Get the autoclicker module to check settings
        Autoclicker autoclicker = Modules.get().get(Autoclicker.class);
        if (autoclicker == null || !autoclicker.shouldBlockObstructedAttacks()) {
            return true; // Allow if autoclicker is not available or setting is disabled
        }

        if (mc.player == null || mc.world == null) {
            return false; // Block if we can't validate
        }

        switch (actionType) {
            case ATTACK_ENTITY:
                return validateEntityAttack(target, autoclicker);
            case ATTACK_BLOCK:
                // Block ALL block attacks when the setting is enabled
                return false;
            default:
                return true; // Allow other actions
        }
    }

    private static boolean validateEntityAttack(Entity targetEntity, Autoclicker autoclicker) {
        if (targetEntity == null) {
            return false;
        }

        // Check range using autoclicker's attack range setting
        double distance = mc.player.distanceTo(targetEntity);
        if (distance > autoclicker.getAttackRange()) {
            return false; // Block - out of range
        }

        // Check if entity is obstructed by blocks
        if (RaycastUtils.isEntityObstructedByBlock(targetEntity)) {
            return false; // Block - obstructed by blocks
        }

        // Check if the player is actually facing the target using raycast
        if (!RaycastUtils.intersectsHitbox(targetEntity, autoclicker.getAttackRange())) {
            return false; // Block - not facing the target properly
        }

        return true; // Allow the attack
    }

    public void processUserInput(Object input) {
        // This is a placeholder for processing user inputs.
        // In a real application, you would likely have a more sophisticated
        // mechanism to identify the type of input and route it to the
        // appropriate module or handler.
        System.out.println("Processing user input: " + input.toString());
    }

    public enum ActionType {
        ATTACK_ENTITY,
        ATTACK_BLOCK,
        USE_ITEM,
        INTERACT
    }
}
