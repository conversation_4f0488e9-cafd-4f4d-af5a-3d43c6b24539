package com.example.addon.modules;

import com.example.addon.AddonTemplate;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.utils.player.FindItemResult;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import com.example.addon.utils.SmoothAimingUtils;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.entity.effect.StatusEffectInstance;
import net.minecraft.entity.effect.StatusEffects;
import net.minecraft.item.Items;
import net.minecraft.component.DataComponentTypes;
import net.minecraft.component.type.PotionContentsComponent;
import net.minecraft.potion.Potion;
import net.minecraft.util.Hand;

import java.util.List;

public class Autopot extends Module {
    private final SettingGroup sgGeneral = settings.getDefaultGroup();

    private final Setting<Double> delay = sgGeneral.add(new DoubleSetting.Builder()
        .name("delay")
        .description("Delay between throwing potions in seconds.")
        .defaultValue(0.5)
        .min(0.0)
        .sliderMax(5.0)
        .build()
    );

    private final Setting<Boolean> lookDown = sgGeneral.add(new BoolSetting.Builder()
        .name("look-down")
        .description("Automatically looks down when throwing potions.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> smoothAiming = sgGeneral.add(new BoolSetting.Builder()
        .name("smooth-aiming")
        .description("Smoothly aims at the target.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> debugMode = sgGeneral.add(new BoolSetting.Builder()
        .name("debug-mode")
        .description("Shows debug messages for potion detection.")
        .defaultValue(true)
        .build()
    );

    // Add settings for which potions to use
    private final Setting<Boolean> useSpeed = sgGeneral.add(new BoolSetting.Builder()
        .name("use-speed")
        .description("Use speed potions.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> useStrength = sgGeneral.add(new BoolSetting.Builder()
        .name("use-strength")
        .description("Use strength potions.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> useFireRes = sgGeneral.add(new BoolSetting.Builder()
        .name("use-fire-resistance")
        .description("Use fire resistance potions.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> useHealing = sgGeneral.add(new BoolSetting.Builder()
        .name("use-healing")
        .description("Use instant health potions.")
        .defaultValue(true)
        .build()
    );

    private int timer;

    public Autopot() {
        super(AddonTemplate.CATEGORY, "autopot", "Automatically throws potions.");
    }

    @Override
    public void onActivate() {
        timer = 0;
        if (debugMode.get()) {
            info("Autopot activated - scanning hotbar for potions...");
            scanAndDebugPotions();
        }
    }

    @EventHandler
    private void onTick(TickEvent.Pre event) {
        if (timer >= delay.get() * 20) {
            throwPotion();
            timer = 0;
        } else {
            timer++;
        }
    }

    private void scanAndDebugPotions() {
        if (!debugMode.get()) return;
        
        info("=== POTION SCAN DEBUG ===");
        for (int i = 0; i < 9; i++) {
            var itemStack = mc.player.getInventory().getStack(i);
            if (itemStack.isEmpty()) continue;
            
            String itemName = itemStack.getItem().toString();
            info("Slot " + i + ": " + itemName);
            
            if (itemStack.isOf(Items.POTION) || itemStack.isOf(Items.SPLASH_POTION)) {
                try {
                    PotionContentsComponent potionContents = itemStack.get(DataComponentTypes.POTION_CONTENTS);
                    if (potionContents == null) {
                        info("  └─ No potion contents");
                        continue;
                    }

                    if (potionContents.potion().isEmpty()) {
                        info("  └─ Empty potion registry entry");
                        continue;
                    }
                    
                    Potion potion = potionContents.potion().get().value();
                    if (potion == null) {
                        info("  └─ Potion value is null");
                        continue;
                    }
                    
                    List<StatusEffectInstance> effects = potion.getEffects();
                    if (effects.isEmpty()) {
                        info("  └─ No effects found");
                        continue;
                    }
                    
                    info("  └─ Effects found:");
                    // Replace the effect matching section with this cleaner version:

                    for (StatusEffectInstance effect : effects) {
                        if (effect == null || effect.getEffectType() == null) {
                            info("    • NULL EFFECT");
                            continue;
                        }
                        
                        // Get the raw effect type for comparison
                        var effectType = effect.getEffectType().value();
                        String effectName = effectType.getName().getString();
                        int amplifier = effect.getAmplifier();
                        int duration = effect.getDuration();
                        
                        info("    • " + effectName + " (Level " + (amplifier + 1) + ", " + duration + " ticks)");
                        
                        // Debug the actual effect type object
                        info("      Effect type object: " + effectType.toString());
                        info("      StatusEffects.SPEED.value(): " + StatusEffects.SPEED.value().toString());
                        
                        // Check for beneficial effects using .value() comparison
                        boolean matches = false;
                        
                        if (effectType == StatusEffects.SPEED.value()) {
                            info("      Found SPEED effect (correct comparison)");
                            if (useSpeed.get()) {
                                matches = true;
                                info("      ✓ Matches Speed criteria");
                            }
                        } else if (effectType == StatusEffects.FIRE_RESISTANCE.value()) {
                            info("      Found FIRE_RESISTANCE effect (correct comparison)");
                            if (useFireRes.get()) {
                                matches = true;
                                info("      ✓ Matches Fire Resistance criteria");
                            }
                        } else if (effectType == StatusEffects.STRENGTH.value()) {
                            info("      Found STRENGTH effect (correct comparison)");
                            if (useStrength.get()) {
                                matches = true;
                                info("      ✓ Matches Strength criteria");
                            }
                        } else if (effectType == StatusEffects.INSTANT_HEALTH.value()) {
                            info("      Found INSTANT_HEALTH effect (correct comparison)");
                            if (useHealing.get()) {
                                matches = true;
                                info("      ✓ Matches Healing criteria");
                            }
                        }
                        
                        if (!matches) {
                            info("      ✗ Does not match any enabled criteria");
                        }
                    }
                    
                } catch (Exception e) {
                    info("  └─ Error reading potion: " + e.getMessage());
                }
            }
        }
        info("=== END POTION SCAN ===");
    }

    
    private void throwPotion() {
    FindItemResult potionResult = InvUtils.findInHotbar(itemStack -> {
        // Check if it's a splash potion or regular potion
        if (!itemStack.isOf(Items.POTION) && !itemStack.isOf(Items.SPLASH_POTION)) {
            return false;
        }

        try {
            PotionContentsComponent potionContents = itemStack.get(DataComponentTypes.POTION_CONTENTS);
            if (potionContents == null) {
                if (debugMode.get()) info("Potion contents is null");
                return false;
            }

            if (potionContents.potion().isEmpty()) {
                if (debugMode.get()) info("Potion registry entry is empty");
                return false;
            }
            
            Potion potion = potionContents.potion().get().value();
            if (potion == null) {
                if (debugMode.get()) info("Potion value is null");
                return false;
            }
            
            List<StatusEffectInstance> effects = potion.getEffects();
            if (effects.isEmpty()) {
                if (debugMode.get()) info("No effects found in potion");
                return false;
            }
            
            // Check for beneficial effects that we don't already have
            for (StatusEffectInstance effect : effects) {
                if (effect == null || effect.getEffectType() == null) continue;
                
                var effectType = effect.getEffectType().value();
                
                // Check if we want this effect type and don't already have it (or it's about to expire)
                boolean shouldUse = false;
                
                if (useSpeed.get() && effectType == StatusEffects.SPEED.value()) {
                    StatusEffectInstance currentEffect = mc.player.getStatusEffect(StatusEffects.SPEED);
                    if (currentEffect == null || currentEffect.getDuration() < 200) { // Less than 10 seconds left
                        shouldUse = true;
                        if (debugMode.get()) info("Need Speed potion - current duration: " + 
                            (currentEffect != null ? currentEffect.getDuration() : 0));
                    }
                } else if (useFireRes.get() && effectType == StatusEffects.FIRE_RESISTANCE.value()) {
                    StatusEffectInstance currentEffect = mc.player.getStatusEffect(StatusEffects.FIRE_RESISTANCE);
                    if (currentEffect == null || currentEffect.getDuration() < 200) {
                        shouldUse = true;
                        if (debugMode.get()) info("Need Fire Resistance potion - current duration: " + 
                            (currentEffect != null ? currentEffect.getDuration() : 0));
                    }
                } else if (useStrength.get() && effectType == StatusEffects.STRENGTH.value()) {
                    StatusEffectInstance currentEffect = mc.player.getStatusEffect(StatusEffects.STRENGTH);
                    if (currentEffect == null || currentEffect.getDuration() < 200) {
                        shouldUse = true;
                        if (debugMode.get()) info("Need Strength potion - current duration: " + 
                            (currentEffect != null ? currentEffect.getDuration() : 0));
                    }
                } else if (useHealing.get() && effectType == StatusEffects.INSTANT_HEALTH.value()) {
                    // Always use healing potions if health is not full
                    if (mc.player.getHealth() < mc.player.getMaxHealth()) {
                        shouldUse = true;
                        if (debugMode.get()) info("Need Healing potion - health: " + mc.player.getHealth() + "/" + mc.player.getMaxHealth());
                    }
                }
                
                if (shouldUse) {
                    if (debugMode.get()) info("Using potion with effect: " + effectType.getName().getString());
                    return true;
                }
            }
            
            if (debugMode.get()) info("All effects already active or not needed");
            return false;
            
        } catch (Exception e) {
            if (debugMode.get()) error("Error checking potion: " + e.getMessage());
            return false;
        }
    });

    if (!potionResult.found()) {
        if (debugMode.get()) info("No suitable potions needed right now.");
        return;
    }

    if (lookDown.get()) {
        if (smoothAiming.get()) {
            try {
                float[] rotations = SmoothAimingUtils.calculateSmoothRotations(
                    mc.player.getYaw(), mc.player.getPitch(), 
                    mc.player.getPos().add(0, -1, 0), 
                    mc.player.getEyePos(), 100.0, 0.1f, 0.5);
                mc.player.setYaw(rotations[0]);
                mc.player.setPitch(rotations[1]);
            } catch (Exception e) {
                // Fallback to simple look down
                mc.player.setPitch(90);
            }
        } else {
            mc.player.setPitch(90);
        }
    }
    
    throwItem(potionResult);
}

private void throwItem(FindItemResult itemResult) {
    InvUtils.swap(itemResult.slot(), false); // false = don't swap back automatically
    mc.interactionManager.interactItem(mc.player, Hand.MAIN_HAND);
}
}