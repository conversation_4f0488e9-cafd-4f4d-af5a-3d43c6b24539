package com.example.addon.modules;

import com.example.addon.AddonTemplate;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.utils.player.FindItemResult;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import net.minecraft.item.Items;

public class Fireworks extends Module {
    public Fireworks() {
        super(AddonTemplate.CATEGORY, "fireworks", "Launches fireworks.");
    }

    @Override
    public void onActivate() {
        FindItemResult fireworks = InvUtils.findInHotbar(Items.FIREWORK_ROCKET);

        if (!fireworks.found()) {
            warning("No fireworks found in hotbar.");
            toggle();
            return;
        }

        InvUtils.swap(fireworks.slot(), false);
        info("Swapped to fireworks.");
        toggle();
    }
}