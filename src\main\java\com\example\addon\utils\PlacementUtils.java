package com.example.addon.utils;

import net.minecraft.client.MinecraftClient;
import net.minecraft.client.network.ClientPlayerEntity;
import net.minecraft.client.network.ClientPlayerInteractionManager;
import net.minecraft.item.ItemStack;
import net.minecraft.network.packet.c2s.play.HandSwingC2SPacket;
import net.minecraft.util.ActionResult;
import net.minecraft.util.Hand;
import net.minecraft.util.hit.BlockHitResult;

import java.util.function.Supplier; // Import Supplier

/**
 * Utility class for handling block placement logic,
 * adapted from LiquidBounce's BlockExtensions.kt.
 */
@SuppressWarnings("unused") // Methods might be called externally or through reflection
public class PlacementUtils {

    private static final MinecraftClient mc = MinecraftClient.getInstance();

    /**
     * Enum for controlling how the arm swing is handled during placement.
     */
    public enum SwingMode {
        DO_NOT_HIDE("DoNotHide", true, (hand) -> {
            ClientPlayerEntity player = mc.player;
            if (player != null) {
                player.swingHand(hand);
            }
        }),
        HIDE_BOTH("HideForBoth", false, (hand) -> { /* No swing */ }),
        HIDE_CLIENT("HideForClient", true, (hand) -> {
            if (mc.getNetworkHandler() != null) {
                mc.getNetworkHandler().sendPacket(new HandSwingC2SPacket(hand));
            }
        }),
        HIDE_SERVER("HideForServer", false, (hand) -> {
            ClientPlayerEntity player = mc.player;
            if (player != null) {
                player.swingHand(hand, false); // Suppress client-side animation
            }
        });

        private final String choiceName;
        private final boolean serverSwing;
        private final SwingAction swingAction; // Functional interface for swing logic

        SwingMode(String choiceName, boolean serverSwing, SwingAction swingAction) {
            this.choiceName = choiceName;
            this.serverSwing = serverSwing;
            this.swingAction = swingAction;
        }

        public String getChoiceName() {
            return choiceName;
        }

        public boolean isServerSwing() {
            return serverSwing;
        }

        public void swing(Hand hand) {
            swingAction.swing(hand);
        }

        @FunctionalInterface
        private interface SwingAction {
            void swing(Hand hand);
        }
    }

    /**
     * Performs a block placement action.
     *
     * @param rayTraceResult The BlockHitResult representing the target block interaction.
     * @param hand The hand used for placement (defaults to MAIN_HAND).
     * @param onPlacementSuccess Supplier that returns true if placement actions (like swinging) should proceed. Defaults to always true.
     * @param onItemUseSuccess Supplier that returns true if item use actions (like swinging) should proceed. Defaults to always true.
     * @param swingMode The mode for handling arm swings (defaults to DO_NOT_HIDE).
     */
    public static void doPlacement(
            BlockHitResult rayTraceResult,
            Hand hand,
            Supplier<Boolean> onPlacementSuccess, // Changed from Runnable to Supplier<Boolean>
            Supplier<Boolean> onItemUseSuccess,   // Changed from Runnable to Supplier<Boolean>
            SwingMode swingMode
    ) {
        ClientPlayerEntity player = mc.player;
        ClientPlayerInteractionManager interactionManager = mc.interactionManager;

        if (player == null || interactionManager == null) {
            return;
        }

        ItemStack stack = player.getStackInHand(hand);
        int count = stack.getCount();

        // This is the core interaction with the block.
        // It sends the PlayerInteractBlockC2SPacket internally.
        ActionResult interactionResult = interactionManager.interactBlock(player, hand, rayTraceResult);

        if (interactionResult == ActionResult.FAIL) {
            return;
        }

        if (interactionResult == ActionResult.PASS) {
            // Cannot place on the block, so try to use the item directly (e.g., buckets, ender pearls).
            handlePass(hand, stack, onItemUseSuccess, swingMode);
            return;
        }

        if (interactionResult.isAccepted()) {
            // Check if an item was consumed or if in creative mode (where items don't decrement)
            boolean wasStackUsed = !stack.isEmpty() && (stack.getCount() != count || player.getAbilities().creativeMode);
            handleActionsOnAccept(hand, interactionResult, wasStackUsed, onPlacementSuccess, swingMode);
        }
    }

    // Default overloads for convenience - now using Suppliers
    public static void doPlacement(BlockHitResult rayTraceResult) {
        doPlacement(rayTraceResult, Hand.MAIN_HAND, () -> true, () -> true, SwingMode.DO_NOT_HIDE);
    }

    public static void doPlacement(BlockHitResult rayTraceResult, Hand hand) {
        doPlacement(rayTraceResult, hand, () -> true, () -> true, SwingMode.DO_NOT_HIDE);
    }

    public static void doPlacement(BlockHitResult rayTraceResult, Hand hand, Supplier<Boolean> onPlacementSuccess) {
        doPlacement(rayTraceResult, hand, onPlacementSuccess, () -> true, SwingMode.DO_NOT_HIDE);
    }

    public static void doPlacement(BlockHitResult rayTraceResult, Hand hand, SwingMode swingMode) {
        doPlacement(rayTraceResult, hand, () -> true, () -> true, swingMode);
    }


    /**
     * Swings item, resets equip progress and hand swing progress.
     *
     * @param wasStackUsed If an item was consumed in order to place the block.
     */
    private static void handleActionsOnAccept(
            Hand hand,
            ActionResult interactionResult,
            boolean wasStackUsed,
            Supplier<Boolean> onPlacementSuccess, // Changed to Supplier<Boolean>
            SwingMode swingMode
    ) {
        // Execute the custom success logic. If it returns true, then perform the swing.
        if (onPlacementSuccess.get()) { // Now correctly calling .get() to retrieve boolean
            swingMode.swing(hand);
        }

        // Reset equip progress if an item was used, to prevent visual glitches
        if (wasStackUsed && mc.gameRenderer != null && mc.gameRenderer.firstPersonRenderer != null) {
            mc.gameRenderer.firstPersonRenderer.resetEquipProgress(hand);
        }
    }


    /**
     * Just interacts with the item in the hand instead of using it on the block,
     * typically for items like buckets or ender pearls when block placement isn't possible.
     */
    private static void handlePass(
            Hand hand,
            ItemStack stack,
            Supplier<Boolean> onItemUseSuccess, // Changed to Supplier<Boolean>
            SwingMode swingMode
    ) {
        ClientPlayerEntity player = mc.player;
        ClientPlayerInteractionManager interactionManager = mc.interactionManager;

        if (player == null || interactionManager == null || stack.isEmpty()) {
            return;
        }

        ActionResult actionResult = interactionManager.interactItem(player, hand);

        // For item interactions, we usually always want a swing if it was successful.
        // LiquidBounce also called handleActionsOnAccept here.
        if (actionResult.isAccepted()) { // If the item interaction was successful
             if (onItemUseSuccess.get()) { // Now correctly calling .get() to retrieve boolean
                swingMode.swing(hand);
            }
        }

        // Reset equip progress if an item was used
        if (mc.gameRenderer != null && mc.gameRenderer.firstPersonRenderer != null) {
            mc.gameRenderer.firstPersonRenderer.resetEquipProgress(hand);
        }
    }
}