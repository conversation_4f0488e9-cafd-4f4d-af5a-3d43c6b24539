package com.example.addon.utils;

import net.minecraft.entity.Entity;

public class ModuleCommunication {
    public static boolean actionTakenThisTick = false;
    private static Entity sprintResetTarget = null;
    private static boolean sprintResetRequested = false;

    public static void requestSprintReset(Entity target) {
        sprintResetTarget = target;
        sprintResetRequested = true;
    }

    public static boolean isSprintResetRequested() {
        return sprintResetRequested;
    }

    public static Entity getSprintResetTarget() {
        return sprintResetTarget;
    }

    public static void clearSprintReset() {
        sprintResetTarget = null;
        sprintResetRequested = false;
    }

    public static void clearSprintResetRequest() {
        sprintResetTarget = null;
        sprintResetRequested = false;
    }

    public static boolean isSprintResetForTarget(Entity target) {
        return sprintResetRequested && 
               (target == null || sprintResetTarget == null || sprintResetTarget.equals(target));
    }

    public static void clearActionFlag() {
        actionTakenThisTick = false;
    }
}