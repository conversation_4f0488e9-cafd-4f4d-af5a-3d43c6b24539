package com.example.addon.modules;

import com.example.addon.utils.SmoothAimingUtils;
import meteordevelopment.meteorclient.events.packets.PacketEvent;
import meteordevelopment.meteorclient.events.render.Render3DEvent;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Categories;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.systems.modules.Modules;
import meteordevelopment.meteorclient.utils.player.FindItemResult;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.block.Blocks;
import net.minecraft.entity.Entity;
import net.minecraft.entity.LivingEntity;
import net.minecraft.entity.decoration.EndCrystalEntity;
import net.minecraft.item.Items;
import net.minecraft.network.packet.Packet;
import net.minecraft.network.packet.c2s.play.HandSwingC2SPacket;
import net.minecraft.network.packet.c2s.play.PlayerInteractEntityC2SPacket;
import net.minecraft.util.ActionResult;
import net.minecraft.util.Hand;
import net.minecraft.util.hit.BlockHitResult;
import net.minecraft.util.math.*;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.function.Predicate;
import java.util.stream.StreamSupport;

public class DTap extends Module {
   private final SettingGroup sgGeneral = settings.getDefaultGroup();
   private final SettingGroup sgAiming = settings.createGroup("Aiming");
   private final SettingGroup sgTiming = settings.createGroup("Timing");

   // --- Settings ---
   private final Setting<Integer> maxCrystals = sgGeneral.add(new IntSetting.Builder()
       .name("max-crystals")
       .description("Maximum number of crystals to place per activation.")
       .defaultValue(3)
       .min(1)
       .max(10)
       .build()
   );

   private final Setting<Double> range = sgGeneral.add(new DoubleSetting.Builder()
       .name("range")
       .description("Maximum range for placing blocks and breaking crystals.")
       .defaultValue(6.0)
       .min(1.0)
       .max(10.0)
       .build()
   );

   private final Setting<Boolean> autoDisable = sgGeneral.add(new BoolSetting.Builder()
       .name("auto-disable")
       .description("Automatically disables after completing crystal sequence.")
       .defaultValue(true)
       .build()
   );

    private final Setting<Boolean> keepTarget = sgGeneral.add(new BoolSetting.Builder()
        .name("keep-target")
        .description("If enabled, the findTarget method will try to keep the current target.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Double> keepTargetDistance = sgGeneral.add(new DoubleSetting.Builder()
        .name("keep-target-distance")
        .description("Maximum distance to keep the current target.")
        .defaultValue(10.0)
        .min(1.0)
        .max(20.0)
        .build()
    );

    private final Setting<Double> fov = sgAiming.add(new DoubleSetting.Builder()
        .name("fov")
        .description("Field of view for the findTarget method.")
        .defaultValue(360.0)
        .min(0.0)
        .max(360.0)
        .sliderMax(360.0)
        .build()
    );

   private final Setting<Integer> placeDelay = sgTiming.add(new IntSetting.Builder()
       .name("place-delay")
       .description("Delay between placing obsidian/crystals in ticks.")
       .defaultValue(2)
       .min(0)
       .max(10)
       .build()
   );

   private final Setting<Integer> breakDelay = sgTiming.add(new IntSetting.Builder()
       .name("break-delay")
       .description("Delay between breaking crystals in ticks.")
       .defaultValue(1)
       .min(0)
       .max(5)
       .build()
   );

   private final Setting<Integer> predictTicks = sgTiming.add(new IntSetting.Builder()
       .name("predict-ticks")
       .description("How many ticks to predict the target's position for optimal obsidian placement.")
       .defaultValue(1)
       .min(0)
       .max(10)
       .build()
   );

   private final Setting<Integer> crystalExplosionDelay = sgTiming.add(new IntSetting.Builder()
       .name("crystal-explosion-delay")
       .description("Delay to wait for crystal explosion and optimal damage.")
       .defaultValue(10)
       .min(0)
       .max(20)
       .build()
   );

   private final Setting<Double> aimSpeed = sgAiming.add(new DoubleSetting.Builder()
       .name("aim-speed")
       .description("Speed of aiming towards targets.")
       .defaultValue(1.25)
       .min(0.025)
       .max(250.0)
       .sliderMax(250.0)
       .build()
   );

   private final Setting<Double> smoothingFactor = sgAiming.add(new DoubleSetting.Builder()
       .name("smoothing-factor")
       .description("Smoothing factor for aiming (higher = smoother).")
       .defaultValue(0.15)
       .min(0.0)
       .max(1.0)
       .sliderMax(1.0)
       .build()
   );

   private final Setting<Double> randomization = sgAiming.add(new DoubleSetting.Builder()
       .name("randomization")
       .description("Amount of randomization in aiming.")
       .defaultValue(0.05)
       .min(0.0)
       .max(1.0)
       .sliderMax(1.0)
       .build()
   );

   private final Setting<Boolean> holdPacketsAfterAttack = sgGeneral.add(new BoolSetting.Builder()
       .name("hold-packets-after-attack")
       .description("Holds back non-swing C2S packets after an attack until the swing packet is sent.")
       .defaultValue(false)
       .build()
   );

   // --- State Variables ---
   private Entity targetEntity = null;
   private LivingEntity currentTarget = null;
   private BlockPos obsidianPos = null;
   private BlockPos crystalPos = null;
   private int crystalCount = 0;
   private int tickCounter = 0;
   private State currentState = State.IDLE;
   private Vec3d targetPos = null;
   private boolean isAiming = false;
   private boolean sentAttackPacket = false;
   private boolean attackedCrystal = false;
   private boolean sequenceStarted = false;
   private int obsidianPlaceAttempts = 0;
   private static final int MAX_OBSIDIAN_ATTEMPTS = 3;
   private final List<Object> queuedPackets = new ArrayList<>();
   private Targets targets;

   public DTap() {
       super(Categories.Combat, "d-tap", "Automatically places obsidian and end crystals when hitting players.");
   }

   private enum State {
       IDLE,
       SEQUENCE_STARTING,
       SCANNING_OBSIDIAN,
       AIMING_OBSIDIAN,
       PLACING_OBSIDIAN,
       WAITING_FOR_OBSIDIAN,
       AIMING_CRYSTAL,
       PLACING_CRYSTAL,
       WAITING_FOR_CRYSTAL,
       AIMING_BREAK,
       BREAKING_CRYSTAL,
       COMPLETE
   }

   @Override
   public void onActivate() {
       targets = Modules.get().get(Targets.class);
       if (targets == null) {
           error("The 'Targets' module is required for DTap to function. Please enable it.");
           toggle();
           return;
       }
       reset();
   }

   private void reset() {
       targetEntity = null;
       currentTarget = null;
       obsidianPos = null;
       crystalPos = null;
       crystalCount = 0;
       tickCounter = 0;
       currentState = State.IDLE;
       stopAiming();
       sentAttackPacket = false;
       attackedCrystal = false;
       sequenceStarted = false;
       obsidianPlaceAttempts = 0;
       queuedPackets.clear();
   }

    @EventHandler
    private void onPacketSend(PacketEvent.Send event) {
        if (mc.player == null || mc.world == null) return;

        if (currentState == State.IDLE && event.packet instanceof PlayerInteractEntityC2SPacket packet) {
            // Heuristic to detect ATTACK vs INTERACT. An attack resets the cooldown.
            if (mc.player.getAttackCooldownProgress(0.5f) < 0.95f) {
                try {
                    int entityId = -1;

                    // This is more robust as it doesn't depend on the field name, only the type.
                    for (java.lang.reflect.Field field : PlayerInteractEntityC2SPacket.class.getDeclaredFields()) {
                        if (field.getType() == int.class) {
                            field.setAccessible(true);
                            entityId = field.getInt(packet);
                            break; // Assume the first and only int is the entity ID
                        }
                    }

                    if (entityId == -1) {
                        throw new NoSuchFieldException("No integer field found in PlayerInteractEntityC2SPacket.");
                    }

                    Entity target = mc.world.getEntityById(entityId);

                    // It's possible for getEntityById to return null. We handle this gracefully.
                    if (target instanceof LivingEntity && shouldStartSequence((LivingEntity) target)) {
                        targetEntity = target;
                        sentAttackPacket = true;
                        info("D-Tap initiated on %s.", target.getName().getString());
                    }

                } catch (Exception e) {
                    // This will now catch NoSuchFieldException if no int field is found, or other reflection errors.
                    error("Failed to get entity from packet. DTap might need an update for your Minecraft version.");
                }
            }
        }

        if (holdPacketsAfterAttack.get() && sentAttackPacket && !(event.packet instanceof HandSwingC2SPacket)) {
            queuedPackets.add(event.packet);
            event.cancel();
        }

        if (event.packet instanceof HandSwingC2SPacket && sentAttackPacket && !sequenceStarted) {
            sentAttackPacket = false;
            sequenceStarted = true;
            currentState = State.SEQUENCE_STARTING;
            tickCounter = 0;
            crystalCount = 0;
            for (Object queuedPacket : queuedPackets) {
                if (queuedPacket instanceof Packet<?>) {
                    mc.player.networkHandler.sendPacket((Packet<?>) queuedPacket);
                }
            }
            queuedPackets.clear();
        }
    }

    @EventHandler
    private void onTick(TickEvent.Pre event) {
        if (mc.player == null || mc.world == null) return;
        tickCounter++;
        switch (currentState) {
            case IDLE: return;
            case SEQUENCE_STARTING:
                if (tickCounter >= 1) {
                    currentState = State.SCANNING_OBSIDIAN;
                    tickCounter = 0;
                    sequenceStarted = false;
                }
                break;
            case SCANNING_OBSIDIAN:
                if (scanForObsidian()) {
                    currentState = State.AIMING_CRYSTAL;
                    tickCounter = 0;
                    startAiming(getCrystalPlacementPos());
                } else {
                    BlockPos placePos = findObsidianPlacementPos();
                    if (placePos != null) {
                        obsidianPos = placePos;
                        currentState = State.AIMING_OBSIDIAN;
                        tickCounter = 0;
                        obsidianPlaceAttempts = 0;
                        startAiming(Vec3d.ofCenter(obsidianPos));
                    } else reset();
                }
                break;
            case AIMING_OBSIDIAN:
                if (tickCounter >= Math.max(1, placeDelay.get() / 2) && isLookingAtTarget()) {
                    currentState = State.PLACING_OBSIDIAN;
                    tickCounter = 0;
                    stopAiming();
                }
                break;
            case PLACING_OBSIDIAN:
                if (placeObsidianInternal()) {
                    currentState = State.WAITING_FOR_OBSIDIAN;
                    tickCounter = 0;
                } else {
                    obsidianPlaceAttempts++;
                    if (obsidianPlaceAttempts >= MAX_OBSIDIAN_ATTEMPTS) reset();
                    else {
                        currentState = State.AIMING_OBSIDIAN;
                        tickCounter = 0;
                        startAiming(Vec3d.ofCenter(obsidianPos));
                    }
                }
                break;
            case WAITING_FOR_OBSIDIAN:
                if (obsidianPos != null && mc.world.getBlockState(obsidianPos).isOf(Blocks.OBSIDIAN)) {
                    currentState = State.AIMING_CRYSTAL;
                    tickCounter = 0;
                    startAiming(getCrystalPlacementPos());
                } else if (tickCounter >= 3) reset();
                break;
            case AIMING_CRYSTAL:
                if (isLookingAtTarget()) {
                    currentState = State.PLACING_CRYSTAL;
                    tickCounter = 0;
                    stopAiming();
                }
                break;
            case PLACING_CRYSTAL:
                if (placeCrystalInternal()) {
                    crystalCount++;
                    currentState = State.WAITING_FOR_CRYSTAL;
                    tickCounter = 0;
                } else {
                    currentState = State.AIMING_CRYSTAL;
                    tickCounter = 0;
                    startAiming(getCrystalPlacementPos());
                }
                break;
            case WAITING_FOR_CRYSTAL:
                if (tickCounter >= crystalExplosionDelay.get()) {
                    EndCrystalEntity crystal = findNearestCrystal();
                    if (crystal != null) {
                        currentState = State.AIMING_BREAK;
                        tickCounter = 0;
                        startAiming(crystal.getPos());
                    } else {
                        currentState = State.COMPLETE;
                        tickCounter = 0;
                    }
                }
                break;
            case AIMING_BREAK:
                if (isLookingAtTarget()) {
                    currentState = State.BREAKING_CRYSTAL;
                    tickCounter = 0;
                    stopAiming();
                }
                break;
            case BREAKING_CRYSTAL:
                EndCrystalEntity crystal = findNearestCrystal();
                if (crystal != null && !attackedCrystal && isLookingAt(crystal.getPos())) {
                    mc.player.swingHand(Hand.MAIN_HAND);
                    mc.interactionManager.attackEntity(mc.player, crystal);
                    attackedCrystal = true;
                }

                if (attackedCrystal || tickCounter >= breakDelay.get()) {
                    if (crystalCount >= maxCrystals.get() || !hasValidCrystalsToBreak()) {
                        currentState = State.COMPLETE;
                        tickCounter = 0;
                    } else {
                        attackedCrystal = false;
                        currentState = State.AIMING_CRYSTAL;
                        tickCounter = 0;
                        if (getCrystalPlacementPos() != null) startAiming(getCrystalPlacementPos());
                    }
                }
                break;
            case COMPLETE:
                if (autoDisable.get()) toggle();
                else reset();
                break;
        }
   }

   private boolean shouldStartSequence(LivingEntity target) {
        if (target == null || target.equals(mc.player)) return false;
        if (!targets.isActive() || !targets.shouldTarget(target)) return false;
        if (mc.player.distanceTo(target) > range.get() + 1) return false;
        return !hasObsidianAbove(target);
    }

   private boolean hasObsidianAbove(LivingEntity target) {
       BlockPos targetPos = target.getBlockPos();
       for (int y = 1; y <= 3; y++) {
           if (mc.world.getBlockState(targetPos.up(y)).isOf(Blocks.OBSIDIAN)) {
               return true;
           }
       }
       return false;
   }

   private LivingEntity findTarget() {
       if (keepTarget.get() && currentTarget != null) {
           if (currentTarget.isAlive() && mc.player.distanceTo(currentTarget) <= keepTargetDistance.get()) {
               if (fov.get() >= 360.0 || isWithinFOV(currentTarget)) {
                   return currentTarget;
               }
           }
           currentTarget = null;
       }

       Predicate<Entity> targetPredicate = entity -> {
           if (entity.equals(mc.player)) return false;
           if (!(entity instanceof LivingEntity)) return false;
           if (isTargetInWeb((LivingEntity)entity) || isPathToTargetBlocked((LivingEntity)entity)) return false;

           if (!targets.isActive() || !targets.shouldTarget(entity)) return false;

           double distance = mc.player.distanceTo(entity);
           boolean withinRange = distance <= range.get();
           boolean withinFOV = fov.get() >= 360.0 || isWithinFOV(entity);

           return withinRange && withinFOV;
       };

       return StreamSupport.stream(mc.world.getEntities().spliterator(), false)
           .filter(targetPredicate)
           .map(entity -> (LivingEntity) entity)
           .min(Comparator.comparingDouble(mc.player::distanceTo))
           .orElse(null);
   }

   private boolean isWithinFOV(Entity entity) {
       if (mc.player == null) return false;
       Vec3d playerLookVec = mc.player.getRotationVec(1.0F);
       Vec3d entityVec = entity.getPos().subtract(mc.player.getEyePos());
       double angle = Math.toDegrees(Math.acos(playerLookVec.dotProduct(entityVec.normalize())));
       return angle <= fov.get() / 2.0;
   }

   private boolean isTargetInWeb(LivingEntity entity) {
       return false;
   }

   private boolean isPathToTargetBlocked(LivingEntity entity) {
       return false;
   }

   private boolean shouldBreakCrystal(EndCrystalEntity crystal) {
       if (targetEntity == null) return false;
       return crystal.getY() >= targetEntity.getY();
   }

   private boolean hasValidCrystalsToBreak() {
       if (targetEntity == null || crystalPos == null) return false;
       return !mc.world.getEntitiesByClass(EndCrystalEntity.class, new Box(crystalPos).expand(2.0), this::shouldBreakCrystal).isEmpty();
   }

    @EventHandler
    private void onRender3D(Render3DEvent event) {
        if (mc.player == null || mc.world == null) return;
        if (isAiming && targetPos != null) updateSmoothAiming((float) event.frameTime);
    }

    private boolean scanForObsidian() {
        if (targetEntity == null) return false;
        BlockPos targetBlockPos = targetEntity.getBlockPos();
        for (int x = -2; x <= 2; x++) {
            for (int y = -1; y <= 2; y++) {
                for (int z = -2; z <= 2; z++) {
                    BlockPos pos = targetBlockPos.add(x, y, z);
                    if (mc.world.getBlockState(pos).isOf(Blocks.OBSIDIAN)) {
                        if (mc.player.getPos().distanceTo(Vec3d.ofCenter(pos)) <= range.get() && canPlaceCrystal(pos.up())) {
                            obsidianPos = pos;
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }

    private BlockPos findObsidianPlacementPos() {
        if (targetEntity == null) return null;
        Vec3d predictedTargetPos = targetEntity.getPos().add(targetEntity.getVelocity().multiply(predictTicks.get()));
        BlockPos targetBlockPos = BlockPos.ofFloored(predictedTargetPos);
        Vec3d playerPos = mc.player.getPos();
        float playerYaw = mc.player.getYaw();
        List<BlockPos> validPositions = new ArrayList<>();
        playerYaw = ((playerYaw % 360) + 360) % 360;

        Direction primaryDir;
        if (playerYaw >= 315 || playerYaw < 45) primaryDir = Direction.SOUTH;
        else if (playerYaw >= 45 && playerYaw < 135) primaryDir = Direction.WEST;
        else if (playerYaw >= 135 && playerYaw < 225) primaryDir = Direction.NORTH;
        else primaryDir = Direction.EAST;

        Direction leftDir = primaryDir.rotateYCounterclockwise();
        Direction rightDir = primaryDir.rotateYClockwise();
        BlockPos leftPos = targetBlockPos.offset(leftDir);
        BlockPos rightPos = targetBlockPos.offset(rightDir);

        if (playerPos.distanceTo(Vec3d.ofCenter(leftPos)) <= range.get() && canPlaceBlock(leftPos) && canPlaceCrystal(leftPos.up())) validPositions.add(leftPos);
        if (playerPos.distanceTo(Vec3d.ofCenter(rightPos)) <= range.get() && canPlaceBlock(rightPos) && canPlaceCrystal(rightPos.up())) validPositions.add(rightPos);

        validPositions.sort(Comparator
            .comparingDouble((BlockPos pos) -> Vec3d.ofCenter(pos).distanceTo(targetEntity.getPos()))
            .thenComparingDouble((BlockPos pos) -> playerPos.distanceTo(Vec3d.ofCenter(pos)))
        );
        return validPositions.isEmpty() ? null : validPositions.get(0);
    }

    private boolean canPlaceBlock(BlockPos pos) {
        return mc.world.getBlockState(pos).isReplaceable() && !mc.world.getBlockState(pos.down()).isAir();
    }

    private boolean canPlaceCrystal(BlockPos pos) {
        if (!mc.world.getBlockState(pos).isReplaceable() || !mc.world.getBlockState(pos.up()).isReplaceable()) return false;
        return mc.world.getOtherEntities(null, new Box(pos).expand(0, 1, 0)).isEmpty();
    }

    private Vec3d getCrystalPlacementPos() {
        if (obsidianPos == null) return null;
        crystalPos = obsidianPos.up();
        return Vec3d.ofCenter(crystalPos);
    }

    private boolean placeObsidianInternal() {
        if (obsidianPos == null) return false;
        if (mc.world.getBlockState(obsidianPos).isOf(Blocks.OBSIDIAN)) return true;
        FindItemResult obsidian = InvUtils.findInHotbar(Items.OBSIDIAN);
        if (!obsidian.found()) { reset(); return false; }
        InvUtils.swap(obsidian.slot(), false);
        ActionResult result = mc.interactionManager.interactBlock(mc.player, Hand.MAIN_HAND, new BlockHitResult(Vec3d.ofCenter(obsidianPos), Direction.UP, obsidianPos, false));
        return result.isAccepted();
    }

    private boolean placeCrystalInternal() {
        if (crystalPos == null || !mc.world.getBlockState(obsidianPos).isOf(Blocks.OBSIDIAN)) return false;
        if (!mc.world.getEntitiesByClass(EndCrystalEntity.class, new Box(crystalPos), e -> true).isEmpty()) return true;
        if (!canPlaceCrystal(crystalPos)) return false;
        FindItemResult crystal = InvUtils.findInHotbar(Items.END_CRYSTAL);
        if (!crystal.found()) { reset(); return false; }
        InvUtils.swap(crystal.slot(), false);
        ActionResult result = mc.interactionManager.interactBlock(mc.player, Hand.MAIN_HAND, new BlockHitResult(Vec3d.ofCenter(obsidianPos), Direction.UP, obsidianPos, false));
        return result.isAccepted();
    }

    private EndCrystalEntity findNearestCrystal() {
        if (crystalPos == null) return null;
        Vec3d crystalCenter = Vec3d.ofCenter(crystalPos);
        List<EndCrystalEntity> crystals = mc.world.getEntitiesByClass(EndCrystalEntity.class, new Box(crystalPos).expand(1.0), e -> e.getPos().distanceTo(crystalCenter) <= 1.5 && shouldBreakCrystal(e));
        if (crystals.isEmpty()) crystals = mc.world.getEntitiesByClass(EndCrystalEntity.class, new Box(obsidianPos).expand(3.0), e -> mc.player.getPos().distanceTo(e.getPos()) <= range.get() && shouldBreakCrystal(e));
        return crystals.stream().min(Comparator.comparingDouble(c -> c.getPos().distanceTo(crystalCenter))).orElse(null);
    }

    private void startAiming(Vec3d target) {
        targetPos = target;
        isAiming = true;
    }

    private void stopAiming() {
        isAiming = false;
        targetPos = null;
    }

    private void updateSmoothAiming(float frameTime) {
        if (mc.player == null || targetPos == null) return;
        float[] newRotations = SmoothAimingUtils.calculateSmoothRotations(mc.player.getYaw(), mc.player.getPitch(), targetPos, mc.player.getEyePos(), aimSpeed.get(), frameTime, smoothingFactor.get());
        if (randomization.get() > 0.0) newRotations = SmoothAimingUtils.addRandomizationToRotations(newRotations, randomization.get());
        mc.player.setYaw(newRotations[0]);
        mc.player.setPitch(newRotations[1]);
    }

    private boolean isLookingAtTarget() {
        if (targetPos == null || mc.player == null) return false;
        Vec3d eyePos = mc.player.getEyePos();
        Vec3d to = targetPos.subtract(eyePos);
        float targetYaw = (float) (Math.toDegrees(Math.atan2(to.z, to.x)) - 90.0f);
        float targetPitch = (float) Math.toDegrees(-Math.atan2(to.y, Math.sqrt(to.x * to.x + to.z * to.z)));
        float yawDiff = Math.abs(SmoothAimingUtils.getShortestAngleDifference(mc.player.getYaw(), targetYaw));
        float pitchDiff = Math.abs(mc.player.getPitch() - targetPitch);
        return yawDiff < 5.0f && pitchDiff < 5.0f;
    }

    private boolean isLookingAt(Vec3d pos) {
        if (pos == null || mc.player == null) return false;
        Vec3d eyePos = mc.player.getEyePos();
        Vec3d to = pos.subtract(eyePos);
        float targetYaw = (float) (Math.toDegrees(Math.atan2(to.z, to.x)) - 90.0f);
        float targetPitch = (float) Math.toDegrees(-Math.atan2(to.y, Math.sqrt(to.x * to.x + to.z * to.z)));
        float yawDiff = Math.abs(SmoothAimingUtils.getShortestAngleDifference(mc.player.getYaw(), targetYaw));
        float pitchDiff = Math.abs(mc.player.getPitch() - targetPitch);
        return yawDiff < 10.0f && pitchDiff < 10.0f;
    }

   @Override
   public String getInfoString() {
       if (currentState == State.IDLE) return "waiting";
       return currentState.toString().toLowerCase().replace("_", " ") + " (" + crystalCount + "/" + maxCrystals.get() + ")";
   }
}