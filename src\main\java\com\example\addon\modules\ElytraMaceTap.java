package com.example.addon.modules;

import meteordevelopment.meteorclient.events.render.Render3DEvent;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.systems.modules.Categories;
import meteordevelopment.meteorclient.systems.modules.Modules;
import meteordevelopment.meteorclient.utils.entity.SortPriority;
import meteordevelopment.meteorclient.utils.entity.TargetUtils;
import meteordevelopment.meteorclient.utils.player.FindItemResult;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.enchantment.Enchantment;
import net.minecraft.enchantment.EnchantmentHelper;
import net.minecraft.enchantment.Enchantments;
import net.minecraft.registry.RegistryKey;
import net.minecraft.registry.entry.RegistryEntry;
import net.minecraft.entity.Entity;
import net.minecraft.entity.EquipmentSlot;
import net.minecraft.entity.LivingEntity;
import com.example.addon.mixins.PlayerInventoryMixin;
import net.minecraft.item.Item;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import net.minecraft.util.Hand;
import net.minecraft.client.option.KeyBinding;
import net.minecraft.util.math.Vec3d;
import net.minecraft.util.math.MathHelper;
import net.minecraft.util.math.Box;
import net.minecraft.util.math.BlockPos;
import net.minecraft.block.Blocks;
import com.example.addon.utils.InputCheckUtils;
import com.example.addon.utils.RaycastUtils;
import com.example.addon.utils.RandomizedAimingUtils;
import com.example.addon.utils.SmoothAimingUtils;

import java.util.function.Predicate;

import static meteordevelopment.meteorclient.MeteorClient.mc;

public class ElytraMaceTap extends Module {
    private LivingEntity currentTarget;
    private LivingEntity previousTarget;

    private final SettingGroup sgGeneral = settings.getDefaultGroup();
    private final SettingGroup sgTiming = settings.createGroup("Timing");
    private final SettingGroup sgAim = settings.createGroup("Aiming");

    private final Setting<Double> range = sgGeneral.add(new DoubleSetting.Builder()
        .name("range")
        .description("The maximum distance to a target to start the sequence.")
        .defaultValue(4.5)
        .min(0)
        .sliderMax(6)
        .build()
    );

    private final Setting<Integer> delayTicks = sgTiming.add(new IntSetting.Builder()
        .name("delay-ticks")
        .description("Delay in ticks between actions.")
        .defaultValue(2)
        .min(0)
        .max(20)
        .build()
    );

    private final Setting<Boolean> autoDisable = sgGeneral.add(new BoolSetting.Builder()
        .name("auto-disable")
        .description("Disables the module after one successful sequence.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> attributeSwap = sgGeneral.add(new BoolSetting.Builder()
        .name("attribute-swap")
        .description("If enabled, starts the sequence from a sword. If disabled, assumes you are already holding the breach mace.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Double> aimSpeed = sgAim.add(new DoubleSetting.Builder()
        .name("aim-speed")
        .description("The speed at which the player aims at the target.")
        .defaultValue(0.5d)
        .range(0.1d, 1.0d)
        .build()
    );

    private final Setting<Double> fov = sgAim.add(new DoubleSetting.Builder()
        .name("fov")
        .description("The field of view (in degrees) within which to target entities.")
        .defaultValue(90.0)
        .min(0.0)
        .max(360.0)
        .sliderMin(0.0)
        .sliderMax(360.0)
        .build()
    );

    private final Setting<Boolean> enableRandomizedAiming = sgAim.add(new BoolSetting.Builder()
        .name("randomized-aiming")
        .description("Enable randomized aiming to make targeting appear more natural and less robotic.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Double> randomizationStrength = sgAim.add(new DoubleSetting.Builder()
        .name("randomization-strength")
        .description("How much randomization to apply to aiming (0.0 = no randomization, 1.0 = full randomization).")
        .defaultValue(0.2)
        .min(0.0)
        .max(1.0)
        .sliderMin(0.0)
        .sliderMax(1.0)
        .visible(() -> enableRandomizedAiming.get())
        .build()
    );

    private final Setting<Double> proximityBias = sgAim.add(new DoubleSetting.Builder()
        .name("proximity-bias")
        .description("How much to prefer aiming points closer to current crosshair position (0.0 = no bias, 1.0 = strong bias).")
        .defaultValue(0.8)
        .min(0.0)
        .max(1.0)
        .sliderMin(0.0)
        .sliderMax(1.0)
        .visible(() -> enableRandomizedAiming.get())
        .build()
    );

    private final Setting<Boolean> preferVitalAreas = sgAim.add(new BoolSetting.Builder()
        .name("prefer-vital-areas")
        .description("Prefer targeting head and torso areas for more realistic aiming patterns.")
        .defaultValue(true)
        .visible(() -> enableRandomizedAiming.get())
        .build()
    );
    
    private final Setting<Double> smoothingFactor = sgAim.add(new DoubleSetting.Builder()
        .name("smoothing-factor")
        .description("How smooth the aiming movement should be (0.0 = instant, 1.0 = very smooth).")
        .defaultValue(0.5)
        .min(0.0)
        .max(1.0)
        .sliderMin(0.0)
        .sliderMax(1.0)
        .build()
    );

    private final Setting<Boolean> enableDynamicTargeting = sgAim.add(new BoolSetting.Builder()
        .name("dynamic-targeting")
        .description("Recalculate aiming area when mouse moves, making targeting more responsive to manual input.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Double> mouseMovementSensitivity = sgAim.add(new DoubleSetting.Builder()
        .name("mouse-sensitivity")
        .description("How sensitive the aimbot is to mouse movement (lower = more sensitive).")
        .defaultValue(0.5)
        .min(0.1)
        .max(2.0)
        .sliderMin(0.1)
        .sliderMax(2.0)
        .visible(() -> enableDynamicTargeting.get())
        .build()
    );

    private final Setting<Integer> targetRecalculateInterval = sgAim.add(new IntSetting.Builder()
        .name("recalculate-interval")
        .description("How often to recalculate target position in milliseconds.")
        .defaultValue(100)
        .min(50)
        .max(500)
        .sliderMin(50)
        .sliderMax(500)
        .visible(() -> enableDynamicTargeting.get())
        .build()
    );

    private final Setting<Boolean> avoidDirectPath = sgAim.add(new BoolSetting.Builder()
        .name("avoid-direct-path")
        .description("Avoid taking the most direct path to target, making movement more natural.")
        .defaultValue(true)
        .visible(() -> enableDynamicTargeting.get())
        .build()
    );

    private final Setting<Double> pathDeviation = sgAim.add(new DoubleSetting.Builder()
        .name("path-deviation")
        .description("How much to deviate from the direct path (0.0 = direct path, 1.0 = maximum deviation).")
        .defaultValue(0.3)
        .min(0.0)
        .max(1.0)
        .sliderMin(0.0)
        .sliderMax(1.0)
        .visible(() -> enableDynamicTargeting.get() && avoidDirectPath.get())
        .build()
    );

    private State currentState;
    private int tickCounter;
    private int originalSlot = -1;
    private int breachMaceSlot = -1;
    private int densityMaceSlot = -1;

    private float lastPlayerYaw = 0.0f;
    private float lastPlayerPitch = 0.0f;
    private boolean hasDetectedMouseMovement = false;
    private Vec3d cachedTargetPosition = null;
    private long lastTargetPositionUpdate = 0;
    private final long TARGET_POSITION_CACHE_MS = 100;
    private final float MOUSE_MOVEMENT_THRESHOLD = 0.5f;

    private Vec3d intermediateTarget = null;
    private long lastIntermediateUpdate = 0;
    private final long INTERMEDIATE_UPDATE_INTERVAL = 200;
    private double pathProgress = 0.0;

    public ElytraMaceTap() {
        super(Categories.Combat, "elytra-mace-tap", "While holding a sword, performs a sequence to execute a powerful mace attack from the air.");
    }

    private enum State {
        IDLE,
        SWAP_TO_BREACH_MACE,
        WAIT_AFTER_BREACH_SWAP, // Added state for delay after swap
        BREACH_ATTACK,
        REMOVE_ELYTRA,
        WAIT_FOR_ELYTRA_SWAP,
        SWAP_TO_DENSITY_MACE,
        WAIT_AFTER_DENSITY_SWAP, // Added state for delay after swap
        DENSITY_ATTACK,
        RESET
    }

    @Override
    public void onActivate() {
        currentState = State.IDLE;
        tickCounter = 0;
        originalSlot = -1;
        breachMaceSlot = -1;
        densityMaceSlot = -1;

        lastPlayerYaw = mc.player != null ? mc.player.getYaw() : 0.0f;
        lastPlayerPitch = mc.player != null ? mc.player.getPitch() : 0.0f;
        hasDetectedMouseMovement = false;
        cachedTargetPosition = null;
        lastTargetPositionUpdate = 0;
        intermediateTarget = null;
        lastIntermediateUpdate = 0;
        pathProgress = 0.0;
    }

    @Override
    public void onDeactivate() {
        if (originalSlot != -1 && mc.player != null && ((PlayerInventoryMixin)mc.player.getInventory()).getSelectedSlot() != originalSlot) {
            InvUtils.swap(originalSlot, true);
        }
    }

    @EventHandler
    private void onTick(TickEvent.Pre event) {
        if (mc.player == null) return;

        Targets targetsModule = Modules.get().get(Targets.class);
        if (targetsModule == null || !targetsModule.isActive()) {
            this.currentTarget = null;
            return;
        }

        Predicate<Entity> targetPredicate = entity -> {
            if (entity.equals(mc.player)) return false;
            return targetsModule.shouldTarget(entity);
        };

        Entity foundTarget = TargetUtils.get(targetPredicate, SortPriority.LowestDistance);

        this.previousTarget = this.currentTarget;
        if (foundTarget instanceof LivingEntity) {
            this.currentTarget = (LivingEntity) foundTarget;
        } else {
            this.currentTarget = null;
        }
    }

    private boolean hasEnchantment(ItemStack stack, RegistryKey<Enchantment> enchantmentRegistryKey) {
        if (stack == null || !stack.hasEnchantments()) {
            return false;
        }
        for (RegistryEntry<Enchantment> entry : EnchantmentHelper.getEnchantments(stack).getEnchantments()) {
            if (entry.getKey().isPresent() && entry.getKey().get().equals(enchantmentRegistryKey)) {
                return true;
            }
        }
        return false;
    }

    private boolean findRequiredItems() {
        if (attributeSwap.get()) {
            originalSlot = ((PlayerInventoryMixin)mc.player.getInventory()).getSelectedSlot();
        } else {
            originalSlot = -1;
        }

        FindItemResult breachMaceResult = InvUtils.findInHotbar(itemStack ->
            itemStack.getItem() == Items.MACE && hasEnchantment(itemStack, Enchantments.BREACH)
        );
        if (!breachMaceResult.found()) {
            warning("Required item not found: Mace with Breach.");
            return false;
        }
        breachMaceSlot = breachMaceResult.slot();

        FindItemResult densityMaceResult = InvUtils.findInHotbar(itemStack ->
            itemStack.getItem() == Items.MACE && hasEnchantment(itemStack, Enchantments.DENSITY)
        );
        if (!densityMaceResult.found()) {
            warning("Required item not found: Mace with Density.");
            return false;
        }
        densityMaceSlot = densityMaceResult.slot();

        return true;
    }

    @EventHandler
    private void onTick(TickEvent.Post event) {
        if (mc.player == null || mc.world == null) return;

        if (currentState == State.IDLE) {
            boolean holdingSword = false;
            if (attributeSwap.get()) {
                Item heldItem = mc.player.getMainHandStack().getItem();
                holdingSword = heldItem == Items.NETHERITE_SWORD || heldItem == Items.DIAMOND_SWORD;
            } else {
                holdingSword = true;
            }
            
            boolean isAirborne = !mc.player.isOnGround();
            boolean targetInRange = currentTarget != null && mc.player.distanceTo(currentTarget) <= range.get() && currentTarget.isAlive();
            boolean targetInFOV = isWithinFOV(currentTarget);

            if (holdingSword && isAirborne && targetInRange && targetInFOV) {
                if (findRequiredItems()) {
                    if (attributeSwap.get()) {
                        currentState = State.SWAP_TO_BREACH_MACE;
                    } else {
                        currentState = State.BREACH_ATTACK; // Skip swap if attributeSwap is off
                    }
                    tickCounter = 0;
                } else {
                    error("Aborting sequence due to missing required maces.");
                    toggle();
                }
            }
        } else {
            tickCounter++;
            if (tickCounter < delayTicks.get()) return; // Wait for delay between actions

            if (currentState == State.WAIT_FOR_ELYTRA_SWAP) {
                ElytraSwap elytraSwapModule = Modules.get().get(ElytraSwap.class);
                if (elytraSwapModule == null || !elytraSwapModule.isActive()) {
                    currentState = State.SWAP_TO_DENSITY_MACE;
                    tickCounter = 0;
                }
                return;
            }

            if (currentTarget == null || !currentTarget.isAlive()) {
                warning("Target lost, resetting sequence.");
                currentState = State.RESET;
                tickCounter = 0;
                return;
            }

            switch (currentState) {
                case SWAP_TO_BREACH_MACE:
                    InvUtils.swap(breachMaceSlot, true);
                    currentState = State.WAIT_AFTER_BREACH_SWAP; // Wait after swap
                    break;

                case WAIT_AFTER_BREACH_SWAP: // New state to introduce delay after swap
                    currentState = State.BREACH_ATTACK;
                    break;

                case BREACH_ATTACK:
                    KeyBinding.onKeyPressed(mc.options.attackKey.getDefaultKey());
                    currentState = State.REMOVE_ELYTRA;
                    break;

                case REMOVE_ELYTRA:
                    ElytraSwap elytraSwapModule = Modules.get().get(ElytraSwap.class);
                    ItemStack equippedChest = mc.player.getEquippedStack(EquipmentSlot.CHEST);

                    if (elytraSwapModule != null && equippedChest.getItem() == Items.ELYTRA) {
                        if (!elytraSwapModule.isActive()) elytraSwapModule.toggle();
                        currentState = State.WAIT_FOR_ELYTRA_SWAP;
                    } else {
                        currentState = State.SWAP_TO_DENSITY_MACE;
                    }
                    break;

                case SWAP_TO_DENSITY_MACE:
                    InvUtils.swap(densityMaceSlot, true);
                    currentState = State.WAIT_AFTER_DENSITY_SWAP; // Wait after swap
                    break;

                case WAIT_AFTER_DENSITY_SWAP: // New state to introduce delay after swap
                    currentState = State.DENSITY_ATTACK;
                    break;

                case DENSITY_ATTACK:
                    KeyBinding.onKeyPressed(mc.options.attackKey.getDefaultKey());
                    currentState = State.RESET;
                    break;

                case RESET:
                    if (originalSlot != -1) {
                        InvUtils.swap(originalSlot, true);
                    }
                    if (autoDisable.get()) {
                        toggle();
                    } else {
                        currentState = State.IDLE;
                    }
                    break;
            }
            tickCounter = 0; // Reset tick counter for the next action/delay
        }
    }

    @EventHandler
    private void onRender3D(Render3DEvent event) {
        if (mc.player == null || mc.world == null) return;

        if (currentState != State.IDLE && currentState != State.RESET && currentTarget != null && currentTarget.isAlive()) {
            if (enableDynamicTargeting.get() && detectMouseMovement()) {
                invalidateTargetPosition();
            }
            aimAtEntity(currentTarget, event.frameTime);
        }
    }

    private boolean detectMouseMovement() {
        if (mc.player == null || !enableDynamicTargeting.get()) return false;

        float currentYaw = mc.player.getYaw();
        float currentPitch = mc.player.getPitch();

        float yawDiff = Math.abs(currentYaw - lastPlayerYaw);
        float pitchDiff = Math.abs(currentPitch - lastPlayerPitch);

        if (yawDiff > 180.0f) {
            yawDiff = 360.0f - yawDiff;
        }

        float threshold = MOUSE_MOVEMENT_THRESHOLD * mouseMovementSensitivity.get().floatValue();
        boolean mouseMovement = yawDiff > threshold || pitchDiff > threshold;

        lastPlayerYaw = currentYaw;
        lastPlayerPitch = currentPitch;

        return mouseMovement;
    }

    private void invalidateTargetPosition() {
        cachedTargetPosition = null;
        lastTargetPositionUpdate = 0;
        hasDetectedMouseMovement = true;
        intermediateTarget = null;
        lastIntermediateUpdate = 0;
        pathProgress = 0.0;
    }

    private Vec3d calculateIndirectPath(Vec3d currentPos, Vec3d finalTarget, LivingEntity target) {
        if (!avoidDirectPath.get()) {
            return finalTarget;
        }

        long currentTime = System.currentTimeMillis();

        if (intermediateTarget == null ||
            (currentTime - lastIntermediateUpdate) > INTERMEDIATE_UPDATE_INTERVAL ||
            hasDetectedMouseMovement) {

            intermediateTarget = calculateCurvedIntermediatePoint(currentPos, finalTarget, target);
            lastIntermediateUpdate = currentTime;
            pathProgress = 0.0;
        }

        pathProgress += 0.02;
        pathProgress = Math.min(pathProgress, 1.0);

        if (intermediateTarget != null) {
            Vec3d currentLookDirection = getPlayerLookDirection();
            Vec3d currentLookTarget = currentPos.add(currentLookDirection.multiply(5.0));

            return calculateBezierPoint(currentLookTarget, intermediateTarget, finalTarget, pathProgress);
        }

        return finalTarget;
    }

    private Vec3d calculateCurvedIntermediatePoint(Vec3d playerPos, Vec3d targetPos, LivingEntity target) {
        Box boundingBox = target.getBoundingBox();
        Vec3d entityCenter = boundingBox.getCenter();

        Vec3d directVector = targetPos.subtract(playerPos).normalize();

        Vec3d perpendicular1 = new Vec3d(-directVector.z, 0, directVector.x).normalize();
        Vec3d perpendicular2 = new Vec3d(0, 1, 0);

        double deviation = pathDeviation.get();
        double horizontalOffset = (Math.random() - 0.5) * 2.0 * deviation * (boundingBox.maxX - boundingBox.minX);
        double verticalOffset = (Math.random() - 0.5) * 2.0 * deviation * (boundingBox.maxY - boundingBox.minY) * 0.5;

        Vec3d intermediatePoint = entityCenter
            .add(perpendicular1.multiply(horizontalOffset))
            .add(perpendicular2.multiply(verticalOffset));

        intermediatePoint = new Vec3d(
            Math.max(boundingBox.minX, Math.min(boundingBox.maxX, intermediatePoint.x)),
            Math.max(boundingBox.minY, Math.min(boundingBox.maxY, intermediatePoint.y)),
            Math.max(boundingBox.minZ, Math.min(boundingBox.maxZ, intermediatePoint.z))
        );

        return intermediatePoint;
    }

    private Vec3d calculateBezierPoint(Vec3d p0, Vec3d p1, Vec3d p2, double t) {
        double oneMinusT = 1.0 - t;
        double oneMinusTSquared = oneMinusT * oneMinusT;
        double tSquared = t * t;
        double twoOneMinusTt = 2.0 * oneMinusT * t;

        return p0.multiply(oneMinusTSquared)
            .add(p1.multiply(twoOneMinusTt))
            .add(p2.multiply(tSquared));
    }

    private Vec3d getPlayerLookDirection() {
        if (mc.player == null) return Vec3d.ZERO;

        float yaw = (float) Math.toRadians(mc.player.getYaw());
        float pitch = (float) Math.toRadians(mc.player.getPitch());

        double x = -Math.sin(yaw) * Math.cos(pitch);
        double y = -Math.sin(pitch);
        double z = Math.cos(yaw) * Math.cos(pitch);

        return new Vec3d(x, y, z);
    }

    private boolean isTargetInWeb(LivingEntity target) {
        BlockPos targetPos = target.getBlockPos();
        return mc.world.getBlockState(targetPos).getBlock() == Blocks.COBWEB ||
               mc.world.getBlockState(targetPos.up()).getBlock() == Blocks.COBWEB;
    }

    private boolean isPathToTargetBlocked(LivingEntity target) {
        Vec3d playerPos = mc.player.getEyePos();
        Vec3d targetPos = target.getEyePos();
        Vec3d direction = targetPos.subtract(playerPos).normalize();
        double distance = playerPos.distanceTo(targetPos);

        for (double d = 0; d < distance; d += 1.0) {
            Vec3d checkPos = playerPos.add(direction.multiply(d));
            BlockPos blockPos = new BlockPos((int)checkPos.x, (int)checkPos.y, (int)checkPos.z);
            if (mc.world.getBlockState(blockPos).getBlock() == Blocks.COBWEB) {
                return true;
            }
        }
        return false;
    }

    private boolean isWithinFOV(Entity entity) {
        if (mc.player == null || entity == null) return false;

        Vec3d playerPos = mc.player.getEyePos();
        Vec3d targetPos = entity.getEyePos();

        double deltaX = targetPos.x - playerPos.x;
        double deltaY = targetPos.y - playerPos.y;
        double deltaZ = targetPos.z - playerPos.z;

        double distanceXZ = Math.sqrt(deltaX * deltaX + deltaZ * deltaZ);

        float targetYaw = (float) Math.toDegrees(Math.atan2(deltaZ, deltaX)) - 90.0F;
        float targetPitch = (float) Math.toDegrees(-Math.atan2(deltaY, distanceXZ));

        targetPitch = MathHelper.clamp(targetPitch, -90.0F, 90.0F);

        float currentYaw = mc.player.getYaw();
        float currentPitch = mc.player.getPitch();

        float yawDiff = Math.abs(getShortestAngleDifference(currentYaw, targetYaw));
        float pitchDiff = Math.abs(targetPitch - currentPitch);

        return yawDiff <= fov.get() / 2.0 && pitchDiff <= fov.get() / 2.0;
    }
    
    private float getShortestAngleDifference(float angle1, float angle2) {
        float diff = angle2 - angle1;

        while (diff > 180.0F) {
            diff -= 360.0F;
        }
        while (diff < -180.0F) {
            diff += 360.0F;
        }

        return diff;
    }

    private void aimAtEntity(LivingEntity target, double frameTime) {
        if (target == null || mc.player == null) return;

        Vec3d playerPos = mc.player.getEyePos();
        float currentYaw = mc.player.getYaw();
        float currentPitch = mc.player.getPitch();

        float[] rotations;

        if (enableRandomizedAiming.get()) {
            if (enableDynamicTargeting.get()) {
                rotations = calculateDynamicRandomizedRotations(
                    currentYaw,
                    currentPitch,
                    target,
                    playerPos,
                    aimSpeed.get() * 25.0,
                    (float) frameTime
                );
            } else {
                rotations = SmoothAimingUtils.calculateSmartRandomizedSmoothRotations(
                    currentYaw,
                    currentPitch,
                    target,
                    playerPos,
                    aimSpeed.get() * 25.0,
                    (float) frameTime,
                    randomizationStrength.get(),
                    proximityBias.get(),
                    preferVitalAreas.get(),
                    smoothingFactor.get()
                );
            }
        } else {
            Vec3d targetPos = target.getEyePos();
            rotations = SmoothAimingUtils.calculateSmoothRotations(
                currentYaw,
                currentPitch,
                targetPos,
                playerPos,
                aimSpeed.get() * 25.0,
                (float) frameTime,
                smoothingFactor.get()
            );
        }

        mc.player.setYaw(rotations[0]);
        mc.player.setPitch(rotations[1]);
    }

    private float[] calculateDynamicRandomizedRotations(float currentYaw, float currentPitch,
                                                       LivingEntity target, Vec3d playerPos,
                                                       double aimSpeed, float deltaTime) {
        long currentTime = System.currentTimeMillis();

        boolean shouldRecalculate = cachedTargetPosition == null ||
                                   hasDetectedMouseMovement ||
                                   (currentTime - lastTargetPositionUpdate) > targetRecalculateInterval.get();

        if (shouldRecalculate) {
            Vec3d baseTargetPosition;
            if (enableRandomizedAiming.get()) {
                baseTargetPosition = RandomizedAimingUtils.getSmartRandomizedTargetPosition(
                    target,
                    playerPos,
                    randomizationStrength.get(),
                    proximityBias.get(),
                    preferVitalAreas.get()
                );
            } else {
                baseTargetPosition = target.getEyePos();
            }

            cachedTargetPosition = calculateIndirectPath(playerPos, baseTargetPosition, target);

            lastTargetPositionUpdate = currentTime;
            hasDetectedMouseMovement = false;
        } else if (avoidDirectPath.get()) {
            Vec3d baseTarget = cachedTargetPosition;
            if (baseTarget != null) {
                cachedTargetPosition = calculateIndirectPath(playerPos, baseTarget, target);
            }
        }

        if (cachedTargetPosition != null) {
            return SmoothAimingUtils.calculateSmoothRotations(
                currentYaw,
                currentPitch,
                cachedTargetPosition,
                playerPos,
                aimSpeed,
                deltaTime,
                0.0 // Aimbot does not use smoothing factor directly, pass 0.0
            );
        } else {
            return SmoothAimingUtils.calculateSmoothRotations(
                currentYaw,
                currentPitch,
                target.getEyePos(),
                playerPos,
                aimSpeed,
                deltaTime,
                0.0 // Aimbot does not use smoothing factor directly, pass 0.0
            );
        }
    }
}