package com.example.addon.utils;

import net.minecraft.client.MinecraftClient;
import net.minecraft.entity.Entity;
import net.minecraft.util.hit.BlockHitResult;
import net.minecraft.util.hit.HitResult;
import net.minecraft.util.math.Box;
import net.minecraft.util.math.Vec3d;
import net.minecraft.world.RaycastContext;

public class RaycastUtils {

    private static final MinecraftClient mc = MinecraftClient.getInstance();
    /**
 * Check if an entity is obstructed by blocks when trying to attack it
 */
    
    public static boolean isEntityObstructedByBlock(Entity targetEntity) {
        if (mc.player == null || mc.world == null || targetEntity == null) {
            return false;
        }
        
        // Perform raycast from player eye to entity center
        Vec3d start = mc.player.getEyePos();
        Vec3d end = targetEntity.getBoundingBox().getCenter();
        
        // Perform block collision raycast
        BlockHitResult blockHit = mc.world.raycast(new RaycastContext(
            start, 
            end, 
            RaycastContext.ShapeType.OUTLINE, 
            RaycastContext.FluidHandling.NONE, 
            mc.player
        ));
        
        // If we hit a block before reaching the entity, it's obstructed
        if (blockHit.getType() == HitResult.Type.BLOCK) {
            // Check if the block hit is closer than the entity
            double blockDistance = start.squaredDistanceTo(blockHit.getPos());
            double entityDistance = start.squaredDistanceTo(end);
            
            return blockDistance < entityDistance;
        }
        
        return false;
    }
    public static boolean intersectsHitbox(Entity entity, double reachDistance) {
        if (mc.player == null || mc.world == null || entity == null) {
            return false;
        }

        Vec3d playerEyePos = mc.player.getEyePos();
        Vec3d playerLookVec = mc.player.getRotationVec(1.0F);
        Vec3d rayEnd = playerEyePos.add(playerLookVec.multiply(reachDistance));

        Box entityBoundingBox = entity.getBoundingBox();

        // Perform a raycast against the entity's bounding box
        return entityBoundingBox.raycast(playerEyePos, rayEnd).isPresent();
    }

    /**
     * Checks if there's a block obstruction between the player and the target position.
     * This is used to determine if block breaking should be allowed.
     * 
     * @param targetPos The target position to raycast to
     * @return true if there's a block obstruction, false if the path is clear
     */
    public static boolean hasBlockObstruction(Vec3d targetPos) {
        if (mc.player == null || mc.world == null) {
            return true; // Assume obstructed if we can't check
        }

        Vec3d playerEyePos = mc.player.getEyePos();
        
        // Perform a raycast from player eye position to target
        RaycastContext context = new RaycastContext(
            playerEyePos,
            targetPos,
            RaycastContext.ShapeType.OUTLINE,
            RaycastContext.FluidHandling.NONE,
            mc.player
        );
        
        BlockHitResult result = mc.world.raycast(context);
        
        // If the raycast hits a block before reaching the target, there's an obstruction
        return result.getType() == HitResult.Type.BLOCK && 
               !result.getBlockPos().equals(new net.minecraft.util.math.BlockPos((int)targetPos.x, (int)targetPos.y, (int)targetPos.z));
    }

    /**
     * Checks if block breaking should be blocked due to obstructions.
     * This prevents trying to break blocks through walls or other blocks.
     * 
     * @param blockPos The position of the block to break
     * @return true if block breaking should be blocked, false if it's allowed
     */
    public static boolean shouldBlockBreakingBeBlocked(net.minecraft.util.math.BlockPos blockPos) {
        if (mc.player == null || mc.world == null) {
            return true; // Block breaking if we can't check
        }

        // Get the center of the target block
        Vec3d blockCenter = Vec3d.ofCenter(blockPos);
        
        return hasBlockObstruction(blockCenter);
    }

    /**
     * Checks if the path from the player's eye position to a target position is obstructed by any entity.
     *
     * @param targetPos The target position to raycast to.
     * @param excludedEntity An entity to exclude from the obstruction check (e.g., the player itself).
     * @return true if an entity obstructs the path, false otherwise.
     */
    public static boolean isPathObstructedByEntity(Vec3d targetPos, Entity excludedEntity) {
        if (mc.player == null || mc.world == null) {
            return false;
        }

        Vec3d playerEyePos = mc.player.getEyePos();
        double distance = playerEyePos.distanceTo(targetPos);

        // Expand the search box slightly to catch entities that are very close to the ray
        Box searchBox = new Box(playerEyePos, targetPos).expand(0.1);

        for (Entity entity : mc.world.getOtherEntities(mc.player, searchBox, e -> e != excludedEntity && e.isAlive() && !e.isSpectator())) {
            // Check if the ray intersects the entity's bounding box
            if (entity.getBoundingBox().raycast(playerEyePos, targetPos).isPresent()) {
                // Further check if the entity is actually between the player and the target
                // This is a simplified check, a more robust one would involve checking the hit result distance
                double entityDistance = playerEyePos.distanceTo(entity.getEyePos());
                if (entityDistance < distance) {
                    return true;
                }
            }
        }
        return false;
    }
}