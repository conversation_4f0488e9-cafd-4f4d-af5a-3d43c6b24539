package com.example.addon.modules;

import com.example.addon.AddonTemplate;
import com.example.addon.mixins.PlayerInventoryMixin;
import com.example.addon.utils.KeyPressSimulator;
import com.example.addon.utils.SmoothAimingUtils;
import meteordevelopment.meteorclient.events.render.Render3DEvent;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.utils.player.FindItemResult;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import meteordevelopment.meteorclient.utils.world.BlockUtils;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.block.Blocks;
import net.minecraft.item.BlockItem;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Direction;
import net.minecraft.util.math.Vec3d;
import net.minecraft.util.math.Vec3i;
import org.lwjgl.glfw.GLFW;

import java.util.ArrayList;
import java.util.List;

public class Clutch extends Module {
    private final SettingGroup sgGeneral = settings.getDefaultGroup();
    private final SettingGroup sgAiming = settings.createGroup("Aiming");

    private final Setting<Double> fallDistance = sgGeneral.add(new DoubleSetting.Builder()
        .name("fall-distance")
        .description("The distance to fall before clutching.")
        .defaultValue(3)
        .min(0)
        .sliderMax(10)
        .build()
    );

    private final Setting<Boolean> voidClutch = sgGeneral.add(new BoolSetting.Builder()
        .name("void-clutch")
        .description("Attempts to clutch when falling into the void.")
        .defaultValue(false)
        .build()
    );

    private final Setting<Integer> delay = sgGeneral.add(new IntSetting.Builder()
        .name("delay")
        .description("Delay between clutch placements in ticks.")
        .defaultValue(3)
        .min(0)
        .max(20)
        .sliderMax(20)
        .build()
    );

    private final Setting<Boolean> useKeyPress = sgGeneral.add(new BoolSetting.Builder()
        .name("use-key-press")
        .description("Simulate key presses for placing blocks.")
        .defaultValue(false)
        .build()
    );

    private final Setting<Integer> placeKey = sgGeneral.add(new IntSetting.Builder()
        .name("place-key")
        .description("The key to simulate for placing blocks (Minecraft key code). Default is Mouse Button 2 (right click).")
        .defaultValue(GLFW.GLFW_MOUSE_BUTTON_2)
        .min(0)
        .build()
    );

    private final Setting<Boolean> smoothAiming = sgAiming.add(new BoolSetting.Builder()
        .name("smooth-aiming")
        .description("Use smooth aiming when placing blocks.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Double> aimSpeed = sgAiming.add(new DoubleSetting.Builder()
        .name("aim-speed")
        .description("Speed of smooth aiming.")
        .defaultValue(0.3)
        .min(0.1)
        .max(1.0)
        .sliderMax(1.0)
        .visible(() -> smoothAiming.get())
        .build()
    );

    private final Setting<Boolean> exponentialSmoothing = sgAiming.add(new BoolSetting.Builder()
        .name("exponential-smoothing")
        .description("Use exponential smoothing for even smoother aiming.")
        .defaultValue(false)
        .visible(() -> smoothAiming.get())
        .build()
    );

    private final Setting<Double> smoothingFactor = sgAiming.add(new DoubleSetting.Builder()
        .name("smoothing-factor")
        .description("Exponential smoothing factor (higher = smoother).")
        .defaultValue(0.7)
        .min(0.0)
        .max(1.0)
        .sliderMax(1.0)
        .visible(() -> smoothAiming.get() && exponentialSmoothing.get())
        .build()
    );

    private final Setting<Boolean> aimAtCenter = sgAiming.add(new BoolSetting.Builder()
        .name("aim-at-center")
        .description("Aim at the center of the block when placing.")
        .defaultValue(true)
        .visible(() -> smoothAiming.get())
        .build()
    );

    private final Setting<Boolean> enableRandomizedAiming = sgAiming.add(new BoolSetting.Builder()
        .name("randomized-aiming")
        .description("Enable randomized aiming to make block placement appear more natural.")
        .defaultValue(true)
        .visible(() -> smoothAiming.get())
        .build()
    );

    private final Setting<Double> randomizationStrength = sgAiming.add(new DoubleSetting.Builder()
        .name("randomization-strength")
        .description("How much randomization to apply to aiming (0.0 = no randomization, 1.0 = full randomization).")
        .defaultValue(0.15)
        .min(0.0)
        .max(1.0)
        .sliderMax(1.0)
        .visible(() -> smoothAiming.get() && enableRandomizedAiming.get())
        .build()
    );

    private int timer = 0;
    private long lastFrameTime = System.nanoTime();
    private BlockPos targetBlock = null;
    private Direction targetDirection = null;
    private boolean isAiming = false;
    private int originalSlot = -1;

    public Clutch() {
        super(AddonTemplate.CATEGORY, "clutch", "Automatically places a block to prevent fall damage.");
    }

    @Override
    public void onActivate() {
        super.onActivate();
        timer = 0;
        targetBlock = null;
        targetDirection = null;
        isAiming = false;
        lastFrameTime = System.nanoTime();
        if (mc.player != null) {
            originalSlot = ((PlayerInventoryMixin) mc.player.getInventory()).getSelectedSlot();
        } else {
            originalSlot = -1;
        }
    }

    @EventHandler
    private void onTick(TickEvent.Pre event) {
        if (mc.player == null || mc.world == null) return;

        long currentTime = System.nanoTime();
        float deltaTime = (currentTime - lastFrameTime) / 1_000_000_000.0f;
        lastFrameTime = currentTime;

        if (timer > 0) {
            timer--;
        }

        if (isAiming || targetBlock != null || timer > 0) {
            if (!smoothAiming.get() || !isAiming) {
                if (timer == 0 && targetBlock != null && targetDirection != null) {
                    placeBlockAtTarget();
                }
            }
            return;
        }

        boolean shouldClutch = false;

        if (mc.player.fallDistance >= fallDistance.get()) {
            shouldClutch = true;
        }

        if (voidClutch.get() && mc.player.getY() < mc.world.getBottomY() && mc.player.getVelocity().y < 0) {
            shouldClutch = true;
        }

        if (shouldClutch) {
            BlockPos playerPos = mc.player.getBlockPos();
            BlockPos bestClutchPos = null;
            Direction bestClutchDirection = null;

            for (int y = -1; y >= -3; y--) {
                BlockPos searchPos = playerPos.add(0, y, 0);
                if (mc.world.getBlockState(searchPos).isAir()) {
                    bestClutchPos = searchPos;
                    bestClutchDirection = Direction.UP;
                    break;
                }
            }

            if (bestClutchPos == null) {
                for (int x = -3; x <= 3; x++) {
                    for (int z = -3; z <= 3; z++) {
                        for (int y = -1; y >= -3; y--) {
                            BlockPos searchPos = playerPos.add(x, y, z);
                            if (!mc.world.getBlockState(searchPos).isAir()) {
                                if (mc.world.getBlockState(searchPos.up()).isAir()) {
                                    bestClutchPos = searchPos.up();
                                    bestClutchDirection = Direction.DOWN;
                                    break;
                                }

                                List<Direction> possibleDirections = new ArrayList<>();
                                if (mc.world.getBlockState(searchPos.east()).isAir()) possibleDirections.add(Direction.EAST);
                                if (mc.world.getBlockState(searchPos.west()).isAir()) possibleDirections.add(Direction.WEST);
                                if (mc.world.getBlockState(searchPos.north()).isAir()) possibleDirections.add(Direction.NORTH);
                                if (mc.world.getBlockState(searchPos.south()).isAir()) possibleDirections.add(Direction.SOUTH);

                                for (Direction dir : possibleDirections) {
                                    BlockPos placePos = searchPos.offset(dir);
                                    if (mc.world.getBlockState(placePos).isAir() && mc.player.getBlockPos().isWithinDistance(placePos, 4.5)) {
                                        if (placePos.getY() < mc.player.getY()) {
                                            bestClutchPos = placePos;
                                            bestClutchDirection = dir.getOpposite();
                                            break;
                                        }
                                    }
                                }
                                if (bestClutchPos != null) break;
                            }
                        }
                        if (bestClutchPos != null) break;
                    }
                    if (bestClutchPos != null) break;
                }
            }


            if (bestClutchPos != null) {
                FindItemResult findBlock = InvUtils.findInHotbar(itemStack -> itemStack.getItem() instanceof BlockItem);
                if (findBlock.found()) {
                    targetBlock = bestClutchPos;
                    targetDirection = bestClutchDirection;
                    isAiming = smoothAiming.get();

                    if (!smoothAiming.get()) {
                        placeBlockAtTarget();
                    }
                } else {
                    warning("No blocks found in hotbar to clutch with.");
                    targetBlock = null;
                    targetDirection = null;
                }
            }
        }
    }

    private void placeBlockAtTarget() {
        if (targetBlock == null || targetDirection == null) return;

        FindItemResult findBlock = InvUtils.findInHotbar(itemStack -> itemStack.getItem() instanceof BlockItem);
        if (!findBlock.found()) {
            warning("No blocks found in hotbar to clutch with.");
            targetBlock = null;
            targetDirection = null;
            swapBackToOriginal();
            return;
        }

        if (originalSlot == -1 && mc.player != null) {
            originalSlot = ((PlayerInventoryMixin) mc.player.getInventory()).getSelectedSlot();
        }

        if (findBlock.getHand() == null && !InvUtils.swap(findBlock.slot(), false)) {
            targetBlock = null;
            targetDirection = null;
            swapBackToOriginal();
            return;
        }

        if (useKeyPress.get()) {
            KeyPressSimulator.pressKey(placeKey.get());
            KeyPressSimulator.releaseKey(placeKey.get());
        } else {
            if (BlockUtils.place(targetBlock, findBlock, false, 0)) {
                info("Clutched at " + targetBlock.toShortString());
            } else {
                warning("Failed to place block at " + targetBlock.toShortString());
            }
        }

        timer = delay.get();
        targetBlock = null;
        targetDirection = null;
        isAiming = false;
        swapBackToOriginal();
    }


    @EventHandler
    private void onRender3d(Render3DEvent event) {
        if (mc.player == null || targetBlock == null || !smoothAiming.get()) {
            return;
        }

        Vec3d playerPos = mc.player.getEyePos();
        Vec3d targetAimPos;

        if (aimAtCenter.get()) {
            targetAimPos = new Vec3d(targetBlock.getX() + 0.5, targetBlock.getY() + 0.5, targetBlock.getZ() + 0.5);
        } else {
            Vec3d blockCenter = new Vec3d(targetBlock.getX() + 0.5, targetBlock.getY() + 0.5, targetBlock.getZ() + 0.5);
            Vec3i unitVector = targetDirection.getOpposite().getVector();
            Vec3d faceNormal = new Vec3d(unitVector.getX(), unitVector.getY(), unitVector.getZ());
            targetAimPos = blockCenter.add(faceNormal.multiply(0.49));

            targetAimPos = new Vec3d(
                Math.max(targetBlock.getX() + 0.01, Math.min(targetBlock.getX() + 0.99, targetAimPos.x)),
                Math.max(targetBlock.getY() + 0.01, Math.min(targetBlock.getY() + 0.99, targetAimPos.y)),
                Math.max(targetBlock.getZ() + 0.01, Math.min(targetBlock.getZ() + 0.99, targetAimPos.z))
            );
        }

        float[] rotations;

        if (enableRandomizedAiming.get()) {
            if (exponentialSmoothing.get()) {
                rotations = SmoothAimingUtils.calculateExponentialSmoothRotations(
                    mc.player.getYaw(),
                    mc.player.getPitch(),
                    targetAimPos,
                    playerPos,
                    aimSpeed.get() * 25.0,
                    (float) event.frameTime,
                    smoothingFactor.get()
                );
            } else {
                rotations = SmoothAimingUtils.calculateSmoothRotations(
                    mc.player.getYaw(),
                    mc.player.getPitch(),
                    targetAimPos,
                    playerPos,
                    aimSpeed.get() * 25.0,
                    (float) event.frameTime,
                    smoothingFactor.get()
                );
            }

            rotations = SmoothAimingUtils.addRandomizationToRotations(rotations, randomizationStrength.get() * 2.0);
        } else {
            if (exponentialSmoothing.get()) {
                rotations = SmoothAimingUtils.calculateExponentialSmoothRotations(
                    mc.player.getYaw(),
                    mc.player.getPitch(),
                    targetAimPos,
                    playerPos,
                    aimSpeed.get() * 25.0,
                    (float) event.frameTime,
                    smoothingFactor.get()
                );
            } else {
                rotations = SmoothAimingUtils.calculateSmoothRotations(
                    mc.player.getYaw(),
                    mc.player.getPitch(),
                    targetAimPos,
                    playerPos,
                    aimSpeed.get() * 25.0,
                    (float) event.frameTime,
                    smoothingFactor.get()
                );
            }
        }

        mc.player.setYaw(rotations[0]);
        mc.player.setPitch(rotations[1]);

        float deltaYaw = rotations[0] - mc.player.getYaw();
        float deltaPitch = rotations[1] - mc.player.getPitch();

        if (Math.abs(deltaYaw) < 0.1 && Math.abs(deltaPitch) < 0.1) {
            isAiming = false;
        }
    }


    private void swapBackToOriginal() {
        if (originalSlot != -1 && mc.player != null) {
            if (((PlayerInventoryMixin) mc.player.getInventory()).getSelectedSlot() != originalSlot) {
                InvUtils.swap(originalSlot, false);
            }
        }
        originalSlot = -1;
    }

    @Override
    public void onDeactivate() {
        super.onDeactivate();
        timer = 0;
        targetBlock = null;
        targetDirection = null;
        isAiming = false;
        lastFrameTime = System.nanoTime();
        swapBackToOriginal();
    }
}